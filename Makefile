# Kubernetes Deployment Makefile

.PHONY: help deploy-dev deploy-staging deploy-prod test clean

# Default environment
ENV ?= dev

help: ## Show this help message
	@echo "🚀 Kubernetes Deployment Commands"
	@echo ""
	@echo "Usage:"
	@echo "  make <target> [ENV=dev|staging|prod]"
	@echo ""
	@echo "Targets:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  %-20s %s\n", $$1, $$2}' $(MAKEFILE_LIST)

deploy-dev: ## Deploy to development environment
	@pwsh ./infrastructure/scripts/quick-deploy.ps1 -Environment dev

deploy-staging: ## Deploy to staging environment  
	@pwsh ./infrastructure/scripts/quick-deploy.ps1 -Environment staging

deploy-prod: ## Deploy to production environment
	@pwsh ./infrastructure/scripts/quick-deploy.ps1 -Environment prod

deploy: ## Deploy to specified environment (use ENV=dev|staging|prod)
	@pwsh ./infrastructure/scripts/quick-deploy.ps1 -Environment $(ENV)

test: ## Test specified environment (use ENV=dev|staging|prod)
	@pwsh ./infrastructure/scripts/test.ps1 -Environment $(ENV)

test-dev: ## Test development environment
	@pwsh ./infrastructure/scripts/test.ps1 -Environment dev

test-staging: ## Test staging environment
	@pwsh ./infrastructure/scripts/test.ps1 -Environment staging

test-prod: ## Test production environment
	@pwsh ./infrastructure/scripts/test.ps1 -Environment prod

clean: ## Clean specified environment (use ENV=dev|staging|prod)
	@pwsh ./infrastructure/scripts/cleanup.ps1 -Environment $(ENV)

clean-all: ## Clean all environments
	@pwsh ./infrastructure/scripts/cleanup.ps1 -Environment all

clean-all-full: ## Clean all environments including shared services
	@pwsh ./infrastructure/scripts/cleanup.ps1 -Environment all -IncludeSharedServices

update-creds: ## Update credentials for specified environment (use ENV=dev|staging|prod)
	@pwsh ./infrastructure/scripts/update-credentials.ps1 -Environment $(ENV)

status: ## Show cluster status
	@echo "🔍 Cluster Status:"
	@kubectl get nodes
	@echo ""
	@echo "📦 Namespaces:"
	@kubectl get ns | grep -E "(dev|staging|prod|default)"
	@echo ""
	@echo "🐳 All Pods:"
	@kubectl get pods -A | grep -E "(postgres|minio|keycloak|traefik)"

pods: ## Show pods status
	@echo "🐳 Pods in default namespace:"
	@kubectl get pods -n default
	@echo ""
	@echo "🐳 Pods in $(ENV) namespace:"
	@kubectl get pods -n $(ENV) 2>/dev/null || echo "Namespace $(ENV) not found"

logs-traefik: ## Show Traefik logs
	@kubectl logs -n default deployment/traefik --tail=50

logs-postgres: ## Show PostgreSQL logs  
	@kubectl logs -n default statefulset/postgres --tail=50

logs-minio: ## Show MinIO logs
	@kubectl logs -n default statefulset/minio --tail=50

logs-keycloak: ## Show Keycloak logs
	@kubectl logs -n default statefulset/keycloak --tail=50

restart-traefik: ## Restart Traefik
	@kubectl rollout restart deployment/traefik -n default

restart-postgres: ## Restart PostgreSQL
	@kubectl rollout restart statefulset/postgres -n default

restart-minio: ## Restart MinIO
	@kubectl rollout restart statefulset/minio -n default

restart-keycloak: ## Restart Keycloak
	@kubectl rollout restart statefulset/keycloak -n default

port-forward-postgres: ## Port forward PostgreSQL (localhost:5432)
	@echo "📡 PostgreSQL available at localhost:5432"
	@kubectl port-forward -n default svc/postgres-service 5432:5432

port-forward-minio: ## Port forward MinIO API (localhost:9000)
	@echo "📡 MinIO API available at localhost:9000"
	@kubectl port-forward -n default svc/minio-api-service 9000:9000

port-forward-minio-console: ## Port forward MinIO Console (localhost:9001)
	@echo "📡 MinIO Console available at localhost:9001"
	@kubectl port-forward -n default svc/minio-console-service 9001:9001

port-forward-keycloak: ## Port forward Keycloak (localhost:8080)
	@echo "📡 Keycloak available at localhost:8080"
	@kubectl port-forward -n default svc/keycloak-service 8080:8080

show-creds: ## Show credentials for specified environment (use ENV=dev|staging|prod)
	@echo "📋 Credentials for $(ENV) environment:"
	@cat ./infrastructure/credentials-$(ENV).txt 2>/dev/null || echo "Credentials file not found. Run 'make deploy ENV=$(ENV)' first."

# Quick aliases
dev: deploy-dev test-dev ## Deploy and test development environment
staging: deploy-staging test-staging ## Deploy and test staging environment  
prod: deploy-prod test-prod ## Deploy and test production environment
