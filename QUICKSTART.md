# 🚀 Quick Start Guide

## Triển khai nhanh trong 5 phút

### Bước 1: <PERSON><PERSON><PERSON> bị
```powershell
# Clone repo và di chuyển vào thư mục
cd d:\K8s\k8s-deployment\infrastructure
```

### Bước 2: C<PERSON><PERSON> nhật domain
Thay thế `yourdomain.com` trong các file sau:
- `base/traefik.yaml` (dòng email Let's Encrypt)
- `environments/*/ingress.yaml` (tất cả các domain)

### Bước 3: Triển khai
```powershell
# Triển khai môi trường dev
.\scripts\quick-deploy.ps1 -Environment dev

# Kiểm tra trạng thái
.\scripts\test.ps1 -Environment dev
```

### Bước 4: L<PERSON>y thông tin đăng nhập
```powershell
# Xem file credentials
Get-Content .\credentials-dev.txt
```

## 📋 Tài khoản quản trị mặc định

<PERSON>hi chạy script, bạn sẽ có:

### 🗄️ PostgreSQL Database
- **Host**: `postgres-service.default.svc.cluster.local:5432`
- **Username**: `admin`
- **Password**: `<tạo ngẫu nhiên>`
- **Databases**: 
  - `dev` (cho môi trường dev)
  - `staging` (cho môi trường staging)  
  - `prod` (cho môi trường production)
  - `keycloak` (cho Keycloak)

### 💾 MinIO Object Storage
- **Console URL**: `https://minio-{env}.yourdomain.com`
- **API URL**: `https://minio-api-{env}.yourdomain.com`
- **Access Key**: `admin`
- **Secret Key**: `<tạo ngẫu nhiên>`
- **Buckets**:
  - `dev-bucket` (cho môi trường dev)
  - `staging-bucket` (cho môi trường staging)
  - `prod-bucket` (cho môi trường production)

### 🔐 Keycloak SSO
- **URL**: `https://keycloak-{env}.yourdomain.com`
- **Username**: `admin`
- **Password**: `<tạo ngẫu nhiên>`
- **Realm**: `shared-realm` (dùng chung cho tất cả môi trường)
- **Clients**:
  - `cms-client` (Client Secret tạo ngẫu nhiên)
  - `api-client` (Client Secret tạo ngẫu nhiên)
  - `frontend-client` (Public client)

### 🌐 Traefik API Gateway
- **Dashboard URL**: `https://traefik-{env}.yourdomain.com`
- **Username**: `admin`
- **Password**: `<tạo ngẫu nhiên>`
- **Features**:
  - Automatic HTTPS với Let's Encrypt
  - Load balancing
  - Basic authentication cho dashboard

## 🔗 URLs theo môi trường

### Development (`-Environment dev`)
```
🌐 Traefik Dashboard: https://traefik-dev.yourdomain.com
💾 MinIO Console:     https://minio-dev.yourdomain.com  
🔌 MinIO API:         https://minio-api-dev.yourdomain.com
🔐 Keycloak:          https://keycloak-dev.yourdomain.com
```

### Staging (`-Environment staging`)
```
🌐 Traefik Dashboard: https://traefik-staging.yourdomain.com
💾 MinIO Console:     https://minio-staging.yourdomain.com
🔌 MinIO API:         https://minio-api-staging.yourdomain.com  
🔐 Keycloak:          https://keycloak-staging.yourdomain.com
```

### Production (`-Environment prod`)
```
🌐 Traefik Dashboard: https://traefik.yourdomain.com
💾 MinIO Console:     https://minio.yourdomain.com
🔌 MinIO API:         https://minio-api.yourdomain.com
🔐 Keycloak:          https://keycloak.yourdomain.com
```

## 🛠️ Scripts có sẵn

| Script | Mô tả |
|--------|--------|
| `quick-deploy.ps1` | Triển khai nhanh một môi trường |
| `deploy.ps1` | Triển khai đầy đủ với nhiều tùy chọn |
| `test.ps1` | Kiểm tra trạng thái services |
| `cleanup.ps1` | Dọn dẹp, xóa môi trường |
| `update-credentials.ps1` | Cập nhật mật khẩu |

## 🔧 Lệnh hữu ích

```powershell
# Xem tất cả pods
kubectl get pods -A

# Xem logs của một service
kubectl logs -n default statefulset/postgres --tail=50

# Restart một service
kubectl rollout restart statefulset/postgres -n default

# Port forward để test local
kubectl port-forward -n default svc/postgres-service 5432:5432
kubectl port-forward -n default svc/minio-api-service 9000:9000

# Truy cập shell của pod
kubectl exec -it postgres-0 -n default -- bash
kubectl exec -it minio-0 -n default -- sh
```

## ⚠️ Lưu ý quan trọng

1. **Domain Configuration**: Nhớ thay đổi `yourdomain.com` thành domain thực của bạn
2. **SSL Certificates**: Let's Encrypt cần domain thực để issue certificates
3. **Passwords**: Được lưu trong file `credentials-{env}.txt`, giữ an toàn
4. **Shared Services**: PostgreSQL, MinIO, Keycloak chỉ có 1 instance nhưng chia database/bucket/client riêng
5. **Namespaces**: Mỗi environment có namespace riêng cho ingress, shared services ở namespace `default`
