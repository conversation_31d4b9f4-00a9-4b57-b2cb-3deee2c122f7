# Kubernetes Cluster Inventory Report

**Thời gian tạo báo cáo:** 24/07/2025  
**Cluster endpoint:** https://************:6443  

## 1. Tổng Quan Cluster

### 1.1 Thông Tin Cluster
- **Control Plane:** https://************:6443
- **CoreDNS:** https://************:6443/api/v1/namespaces/kube-system/services/kube-dns:dns/proxy
- **Kubernetes Version:** v1.28.15

### 1.2 Nodes trong Cluster

| Node Name   | Role          | Status | Age  | Internal IP  | OS Image           | Kernel Version     | Container Runtime   |
| ----------- | ------------- | ------ | ---- | ------------ | ------------------ | ------------------ | ------------------- |
| master      | control-plane | Ready  | 227d | ************ | Ubuntu 22.04.5 LTS | 5.15.0-138-generic | containerd://1.7.24 |
| warehouse01 | worker        | Ready  | 36d  | ************ | Ubuntu 22.04.2 LTS | 5.15.0-141-generic | containerd://1.7.27 |
| warehouse03 | worker        | Ready  | 36d  | ************ | Ubuntu 22.04.3 LTS | 5.15.0-141-generic | containerd://1.7.27 |

### 1.3 Tài Nguyên Sử Dụng Nodes

| Node Name   | CPU(cores) | CPU(%) | Memory(bytes) | Memory(%) |
| ----------- | ---------- | ------ | ------------- | --------- |
| master      | 302m       | 5%     | 5348Mi        | 68%       |
| warehouse01 | 152m       | 0%     | 4299Mi        | 17%       |
| warehouse03 | 243m       | 1%     | 5394Mi        | 22%       |

## 2. Namespaces và Ứng Dụng

### 2.1 Danh Sách Namespaces
- `car-serv` - Ứng dụng Car Service
- `cattle-fleet-system` - Rancher Fleet Agent
- `cattle-system` - Rancher Cattle System
- `default` - Namespace mặc định với demo applications
- `ingress-nginx` - NGINX Ingress Controller
- `kube-system` - Kubernetes system components
- `local-path-storage` - Local Path Provisioner

## 3. Chi Tiết Dịch Vụ Theo Namespace

### 3.1 Namespace: car-serv
**Mục đích:** Ứng dụng Car Service

#### Deployments:
- `car-serv-deployment` (2/2 replicas ready)

#### Pods:
| Pod Name                             | Status  | Node        | IP             | CPU | Memory |
| ------------------------------------ | ------- | ----------- | -------------- | --- | ------ |
| car-serv-deployment-674d8454f8-4nsbw | Running | warehouse03 | ************   | 0m  | 13Mi   |
| car-serv-deployment-674d8454f8-hl8pr | Running | warehouse01 | ************** | 0m  | 16Mi   |

#### Services:
- `car-serv-service` - ClusterIP: **************, Port: 80/TCP

#### Ingress:
- `car-serv-ingress` - Host: car-serv-onpre.devopsedu.vn, Class: nginx

### 3.2 Namespace: cattle-fleet-system
**Mục đích:** Rancher Fleet Management

#### Pods:
| Pod Name      | Status        | Node        | IP          | CPU | Memory |
| ------------- | ------------- | ----------- | ----------- | --- | ------ |
| fleet-agent-0 | Running (2/2) | warehouse03 | *********** | 2m  | 38Mi   |

#### Services:
- `fleet-agent` - ClusterIP: None (Headless service)

### 3.3 Namespace: cattle-system
**Mục đích:** Rancher Cattle System Components

#### Deployments:
- `cattle-cluster-agent` (2/2 replicas ready)
- `rancher-webhook` (1/1 replicas ready)

#### Pods:
| Pod Name                              | Status  | Node        | IP             | CPU | Memory |
| ------------------------------------- | ------- | ----------- | -------------- | --- | ------ |
| cattle-cluster-agent-78768684dc-hpwfw | Running | warehouse03 | ************   | 15m | 464Mi  |
| cattle-cluster-agent-78768684dc-qr6qt | Running | master      | *************  | 11m | 544Mi  |
| rancher-webhook-64c746b4cd-jgg4r      | Running | warehouse01 | ************** | 4m  | 34Mi   |

#### Services:
- `cattle-cluster-agent` - ClusterIP: *************, Ports: 80/TCP, 443/TCP
- `rancher-webhook` - ClusterIP: **************, Port: 443/TCP

### 3.4 Namespace: default
**Mục đích:** Demo Applications

#### Deployments:
- `demo-client` (2/2 replicas ready)
- `demo-server` (2/2 replicas ready)

#### Pods:
| Pod Name                     | Status  | Node        | IP             | CPU | Memory |
| ---------------------------- | ------- | ----------- | -------------- | --- | ------ |
| demo-client-6f598f4c6c-22sfk | Running | warehouse01 | ************** | 3m  | 293Mi  |
| demo-client-6f598f4c6c-9868b | Running | warehouse03 | ***********    | 2m  | 498Mi  |
| demo-server-594b9987dc-8l7zm | Running | warehouse03 | ************   | 2m  | 492Mi  |
| demo-server-594b9987dc-krg9m | Running | warehouse01 | ************** | 3m  | 210Mi  |

#### Services:
- `demo-client` - ClusterIP: **************, Port: 80/TCP
- `demo-server` - ClusterIP: ************, Port: 80/TCP
- `demo-server-nodeport` - NodePort: *************, Port: 8888:30888/TCP
- `kubernetes` - ClusterIP: *********, Port: 443/TCP

### 3.5 Namespace: ingress-nginx
**Mục đích:** NGINX Ingress Controller

#### Deployments:
- `ingress-nginx-controller` (1/1 replicas ready)

#### Pods:
| Pod Name                                 | Status  | Node        | IP           | CPU | Memory |
| ---------------------------------------- | ------- | ----------- | ------------ | --- | ------ |
| ingress-nginx-controller-ddf5756b8-d9rwk | Running | warehouse03 | ************ | 5m  | 204Mi  |

#### Services:
- `ingress-nginx-controller` - NodePort: **************, Ports: 80:30080/TCP, 443:30443/TCP
- `ingress-nginx-controller-admission` - ClusterIP: **************, Port: 443/TCP

### 3.6 Namespace: kube-system
**Mục đích:** Kubernetes System Components

#### Deployments:
- `calico-kube-controllers` (1/1 replicas ready)
- `coredns` (2/2 replicas ready)
- `metric-server-metrics-server` (1/1 replicas ready)

#### Pods hệ thống:
| Pod Name                                      | Status  | Node        | IP             | CPU | Memory | Chức năng                 |
| --------------------------------------------- | ------- | ----------- | -------------- | --- | ------ | ------------------------- |
| calico-kube-controllers-74d5f9d7bb-fbzmp      | Running | warehouse01 | ************** | 5m  | 14Mi   | Network Policy Controller |
| calico-node-78xrk                             | Running | warehouse01 | ************   | 58m | 107Mi  | CNI Network Plugin        |
| calico-node-nqz6b                             | Running | warehouse03 | ************   | 74m | 107Mi  | CNI Network Plugin        |
| calico-node-tfvg4                             | Running | master      | ************   | 28m | 114Mi  | CNI Network Plugin        |
| coredns-5dd5756b68-rb6qm                      | Running | master      | *************  | 2m  | 20Mi   | DNS Server                |
| coredns-5dd5756b68-rcfcl                      | Running | master      | *************  | 2m  | 17Mi   | DNS Server                |
| etcd-master                                   | Running | master      | ************   | 32m | 89Mi   | Key-Value Store           |
| kube-apiserver-master                         | Running | master      | ************   | 97m | 600Mi  | API Server                |
| kube-controller-manager-master                | Running | master      | ************   | 15m | 78Mi   | Controller Manager        |
| kube-proxy-5p5t4                              | Running | warehouse03 | ************   | 1m  | 18Mi   | Network Proxy             |
| kube-proxy-g7nvm                              | Running | master      | ************   | 1m  | 22Mi   | Network Proxy             |
| kube-proxy-k9p5k                              | Running | warehouse01 | ************   | 2m  | 18Mi   | Network Proxy             |
| kube-scheduler-master                         | Running | master      | ************   | 4m  | 29Mi   | Scheduler                 |
| metric-server-metrics-server-6d5f479985-p4kvw | Running | warehouse01 | ************** | 7m  | 22Mi   | Metrics Server            |

#### Services hệ thống:
- `devopseduvn-kube-prometheu-kubelet` - ClusterIP: None, Ports: 10250/TCP, 10255/TCP, 4194/TCP
- `kube-dns` - ClusterIP: *********0, Ports: 53/UDP, 53/TCP, 9153/TCP
- `metric-server-metrics-server` - ClusterIP: **************, Port: 443/TCP

### 3.7 Namespace: local-path-storage
**Mục đích:** Local Path Storage Provisioner

#### Deployments:
- `local-path-provisioner` (1/1 replicas ready)

#### Pods:
| Pod Name                                | Status  | Node        | IP             | CPU | Memory |
| --------------------------------------- | ------- | ----------- | -------------- | --- | ------ |
| local-path-provisioner-6548cc785f-pzlrj | Running | warehouse01 | ************** | 1m  | 9Mi    |

## 4. Exposed Ports và Network Services

### 4.1 NodePort Services (Accessible từ bên ngoài)
- **demo-server-nodeport:** Port 30888 → 8888/TCP
- **ingress-nginx-controller:** 
  - Port 30080 → 80/TCP (HTTP)
  - Port 30443 → 443/TCP (HTTPS)

### 4.2 Ingress Endpoints
- **car-serv-onpre.devopsedu.vn** → car-serv service (port 80)

### 4.3 Internal Cluster IPs
- **Kubernetes API:** *********:443
- **CoreDNS:** *********0:53
- **Car Service:** **************:80
- **Demo Client:** **************:80
- **Demo Server:** ************:80
- **Ingress Controller:** **************:80,443

## 5. Network Architecture

### 5.1 Pod Network (Calico CNI)
- **Pod CIDR:** **********/16
- **master node pods:** 172.16.219.x/24
- **warehouse01 node pods:** 172.16.163.x/24
- **warehouse03 node pods:** 172.16.68.x/24

### 5.2 Node Network
- **master:** ************
- **warehouse01:** ************
- **warehouse03:** ************

## 6. Tóm Tắt Tài Nguyên

### 6.1 Tổng số resources:
- **Nodes:** 3 (1 master, 2 workers)
- **Pods:** 26 pods đang chạy
- **Services:** 15 services
- **Deployments:** 9 deployments
- **Ingress:** 1 ingress rule

### 6.2 Ứng dụng chính:
1. **Car Service** - Ứng dụng car-serv với ingress public
2. **Demo Applications** - Demo client/server với NodePort exposure
3. **Rancher Management** - Cattle system và Fleet agent
4. **System Components** - Kubernetes core, Calico networking, CoreDNS, metrics server

### 6.3 External Access Points:
- **HTTP:** Node IPs:30080 → Ingress Controller
- **HTTPS:** Node IPs:30443 → Ingress Controller  
- **Demo Server:** Node IPs:30888 → Demo Server
- **Domain:** car-serv-onpre.devopsedu.vn → Car Service

---
*Báo cáo được tạo tự động bởi kubectl vào ngày 24/07/2025*
