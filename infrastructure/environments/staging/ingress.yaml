apiVersion: traefik.containo.us/v1alpha1
kind: IngressRoute
metadata:
  name: traefik-dashboard-staging
  namespace: staging
spec:
  entryPoints:
    - websecure
  routes:
    - match: Host(`traefik-staging.yourdomain.com`)
      kind: Rule
      middlewares:
        - name: dashboard-auth
          namespace: default
      services:
        - name: traefik-dashboard
          namespace: default
          port: 8080
  tls:
    certResolver: letsencrypt
---
apiVersion: traefik.containo.us/v1alpha1
kind: IngressRoute
metadata:
  name: minio-console-staging
  namespace: staging
spec:
  entryPoints:
    - websecure
  routes:
    - match: Host(`minio-staging.yourdomain.com`)
      kind: Rule
      middlewares:
        - name: minio-console-headers
          namespace: default
        - name: minio-console-websocket
          namespace: default
      services:
        - name: minio-console-service
          namespace: default
          port: 9001
  tls:
    certResolver: letsencrypt
---
apiVersion: traefik.containo.us/v1alpha1
kind: IngressRoute
metadata:
  name: minio-api-staging
  namespace: staging
spec:
  entryPoints:
    - websecure
  routes:
    - match: Host(`minio-api-staging.yourdomain.com`)
      kind: Rule
      services:
        - name: minio-api-service
          namespace: default
          port: 9000
  tls:
    certResolver: letsencrypt
---
apiVersion: traefik.containo.us/v1alpha1
kind: IngressRoute
metadata:
  name: keycloak-staging
  namespace: staging
spec:
  entryPoints:
    - websecure
  routes:
    - match: Host(`keycloak-staging.yourdomain.com`)
      kind: Rule
      services:
        - name: keycloak-service
          namespace: default
          port: 8080
  tls:
    certResolver: letsencrypt
---
# HTTP-based routes for direct IP access
apiVersion: traefik.containo.us/v1alpha1
kind: IngressRoute
metadata:
  name: minio-console-http-staging
  namespace: staging
spec:
  entryPoints:
    - web
  routes:
    - match: PathPrefix(`/minio-console/`)
      kind: Rule
      middlewares:
        - name: minio-console-stripprefix
          namespace: default
        - name: minio-console-headers
          namespace: default
        - name: minio-console-websocket
          namespace: default
      services:
        - name: minio-console-service
          namespace: default
          port: 9001
