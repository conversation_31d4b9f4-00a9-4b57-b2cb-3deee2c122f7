apiVersion: apps/v1
kind: Deployment
metadata:
  name: pgadmin
  namespace: default
  labels:
    app: pgadmin
spec:
  replicas: 1
  selector:
    matchLabels:
      app: pgadmin
  template:
    metadata:
      labels:
        app: pgadmin
    spec:
      containers:
        - name: pgadmin
          image: dpage/pgadmin4:latest
          ports:
            - containerPort: 80
              name: http
          env:
            - name: PGADMIN_DEFAULT_EMAIL
              valueFrom:
                secretKeyRef:
                  name: pgadmin-secret
                  key: admin-email
            - name: PGA<PERSON><PERSON>_DEFAULT_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: pgadmin-secret
                  key: admin-password
            - name: SCRIPT_NAME
              value: /pgadmin
            - name: PGADMIN_CONFIG_FORCE_SESSION_TIMEOUT
              value: "False"
          volumeMounts:
            - name: pgadmin-storage
              mountPath: /var/lib/pgadmin
          resources:
            limits:
              cpu: 500m
              memory: 1Gi
            requests:
              cpu: 200m
              memory: 512Mi
          livenessProbe:
            httpGet:
              path: /misc/ping
              port: 80
            initialDelaySeconds: 30
            periodSeconds: 30
            timeoutSeconds: 10
          readinessProbe:
            httpGet:
              path: /misc/ping
              port: 80
            initialDelaySeconds: 10
            periodSeconds: 10
            timeoutSeconds: 5
      volumes:
        - name: pgadmin-storage
          persistentVolumeClaim:
            claimName: pgadmin-storage
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: pgadmin-storage
  namespace: default
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 2Gi
---
apiVersion: v1
kind: Service
metadata:
  name: pgadmin-service
  namespace: default
  labels:
    app: pgadmin
spec:
  ports:
    - name: http
      port: 80
      targetPort: 80
  selector:
    app: pgadmin
---
apiVersion: traefik.io/v1alpha1
kind: Middleware
metadata:
  name: pgadmin-headers
  namespace: default
spec:
  headers:
    customRequestHeaders:
      X-Script-Name: "/pgadmin"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: pgadmin-servers
  namespace: default
data:
  servers.json: |
    {
      "Servers": {
        "1": {
          "Name": "PostgreSQL - CMS Database",
          "Group": "CMS Infrastructure", 
          "Host": "postgres-service.default.svc.cluster.local",
          "Port": 5432,
          "MaintenanceDB": "defaultdb",
          "Username": "admin",
          "SSLMode": "prefer",
          "Comment": "PostgreSQL server for CMS project"
        }
      }
    }
