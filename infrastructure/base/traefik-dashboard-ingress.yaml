apiVersion: traefik.io/v1alpha1
kind: Middleware
metadata:
  name: dashboard-auth
  namespace: default
spec:
  basicAuth:
    secret: traefik-dashboard-auth
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: traefik-dashboard
  namespace: default
spec:
  entryPoints:
    - web
    - websecure
  routes:
    - match: Host(`traefik.osp.vn`)
      kind: Rule
      middlewares:
        - name: dashboard-auth
      services:
        - name: traefik-dashboard
          port: 8080
  tls:
    certResolver: letsencrypt
---
apiVersion: traefik.io/v1alpha1
kind: IngressRoute
metadata:
  name: traefik-dashboard-http
  namespace: default
spec:
  entryPoints:
    - web
  routes:
    - match: PathPrefix(`/dashboard`) || PathPrefix(`/api`)
      kind: Rule
      middlewares:
        - name: dashboard-auth
      services:
        - name: traefik-dashboard
          port: 8080
