apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgres
  namespace: default
  labels:
    app: postgres
spec:
  serviceName: postgres-service
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
        - name: postgres
          image: postgres:15-alpine
          ports:
            - containerPort: 5432
              name: postgres
          env:
            - name: POSTGRES_DB
              value: "defaultdb"
            - name: POSTGRES_USER
              valueFrom:
                secretKeyRef:
                  name: postgres-secret
                  key: postgres-user
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: postgres-secret
                  key: postgres-password
            - name: PGDATA
              value: /var/lib/postgresql/data/pgdata
          volumeMounts:
            - name: postgres-storage
              mountPath: /var/lib/postgresql/data
            - name: postgres-init
              mountPath: /docker-entrypoint-initdb.d
          livenessProbe:
            exec:
              command:
                - /bin/sh
                - -c
                - exec pg_isready -U "$POSTGRES_USER" -d "$POSTGRES_DB" -h 127.0.0.1 -p 5432
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            successThreshold: 1
            failureThreshold: 6
          readinessProbe:
            exec:
              command:
                - /bin/sh
                - -c
                - exec pg_isready -U "$POSTGRES_USER" -d "$POSTGRES_DB" -h 127.0.0.1 -p 5432
            initialDelaySeconds: 5
            periodSeconds: 10
            timeoutSeconds: 5
            successThreshold: 1
            failureThreshold: 6
          resources:
            requests:
              memory: "256Mi"
              cpu: "250m"
            limits:
              memory: "512Mi"
              cpu: "500m"
      volumes:
        - name: postgres-init
          configMap:
            name: postgres-init-script
  volumeClaimTemplates:
    - metadata:
        name: postgres-storage
      spec:
        accessModes: ["ReadWriteOnce"]
        resources:
          requests:
            storage: 10Gi
---
apiVersion: v1
kind: Service
metadata:
  name: postgres-service
  namespace: default
  labels:
    app: postgres
spec:
  ports:
    - port: 5432
      targetPort: 5432
      name: postgres
  selector:
    app: postgres
  type: ClusterIP
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgres-init-script
  namespace: default
data:
  init.sql: |
    -- Create databases for different environments if they do not exist
    DO $$
    BEGIN
      IF NOT EXISTS (SELECT FROM pg_database WHERE datname = 'dev') THEN
        PERFORM dblink_exec('dbname=' || current_database(), 'CREATE DATABASE dev');
      END IF;
      IF NOT EXISTS (SELECT FROM pg_database WHERE datname = 'staging') THEN
        PERFORM dblink_exec('dbname=' || current_database(), 'CREATE DATABASE staging');
      END IF;
      IF NOT EXISTS (SELECT FROM pg_database WHERE datname = 'prod') THEN
        PERFORM dblink_exec('dbname=' || current_database(), 'CREATE DATABASE prod');
      END IF;
    END
    $$ LANGUAGE plpgsql;

    -- Grant permissions to admin user
    -- Note: You must connect to each database to grant privileges
    -- Example:
    -- \c dev
    -- GRANT ALL PRIVILEGES ON DATABASE dev TO admin;
    -- \c staging
    -- GRANT ALL PRIVILEGES ON DATABASE staging TO admin;
    -- \c prod
    -- GRANT ALL PRIVILEGES ON DATABASE prod TO admin;
