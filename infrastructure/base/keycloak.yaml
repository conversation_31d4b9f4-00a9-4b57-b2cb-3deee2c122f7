apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: keycloak
  namespace: default
  labels:
    app: keycloak
spec:
  serviceName: keycloak-service
  replicas: 1
  selector:
    matchLabels:
      app: keycloak
  template:
    metadata:
      labels:
        app: keycloak
    spec:
      containers:
        - name: keycloak
          image: quay.io/keycloak/keycloak:23.0
          args:
            - start-dev
          ports:
            - containerPort: 8080
              name: http
          env:
            - name: KEYCLOAK_ADMIN
              valueFrom:
                secretKeyRef:
                  name: keycloak-secret
                  key: admin-username
            - name: <PERSON><PERSON><PERSON><PERSON><PERSON>K_ADMIN_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: keycloak-secret
                  key: admin-password
            - name: KC_HOSTNAME_STRICT
              value: "false"
            - name: KC_HOSTNAME_STRICT_HTTPS
              value: "false"
            - name: KC_HTTP_ENABLED
              value: "true"
            - name: KC_HEALTH_ENABLED
              value: "false"
            - name: KC_METRICS_ENABLED
              value: "true"
            - name: KC_DB
              value: "postgres"
            - name: KC_DB_URL
              value: "**************************************************************************"
            - name: KC_DB_USERNAME
              valueFrom:
                secretKeyRef:
                  name: postgres-secret
                  key: postgres-user
            - name: KC_DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: postgres-secret
                  key: postgres-password
            - name: KC_HTTP_RELATIVE_PATH  # Biến chính để set subpath trong Keycloak 23.0
              value: /keycloak
          volumeMounts:
            - name: keycloak-data
              mountPath: /opt/keycloak/data
            - name: realm-config
              mountPath: /opt/keycloak/data/import
          # livenessProbe:
          #   httpGet:
          #     path: /keycloak/health/live
          #     port: 8080
          #   initialDelaySeconds: 120
          #   periodSeconds: 30
          #   timeoutSeconds: 10
          # readinessProbe:
          #   httpGet:
          #     path: /keycloak/health/ready
          #     port: 8080
          #   initialDelaySeconds: 90
          #   periodSeconds: 10
          #   timeoutSeconds: 5
          resources:
            requests:
              memory: "1Gi"
              cpu: "500m"
            limits:
              memory: "2Gi"
              cpu: "1000m"
      volumes:
        - name: realm-config
          configMap:
            name: keycloak-realm-config
  volumeClaimTemplates:
    - metadata:
        name: keycloak-data
      spec:
        accessModes: ["ReadWriteOnce"]
        resources:
          requests:
            storage: 5Gi
---
apiVersion: v1
kind: Service
metadata:
  name: keycloak-service
  namespace: default
  labels:
    app: keycloak
spec:
  ports:
    - port: 8080
      targetPort: 8080
      name: http
  selector:
    app: keycloak
  type: ClusterIP
---
apiVersion: traefik.io/v1alpha1
kind: Middleware
metadata:
  name: keycloak-headers
  namespace: default
spec:
  headers:
    customRequestHeaders:
      X-Forwarded-Prefix: "/keycloak"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: keycloak-realm-config
  namespace: default
data:
  shared-realm.json: |
    {
      "realm": "shared-realm",
      "enabled": true,
      "displayName": "Shared Realm",
      "displayNameHtml": "<div class=\"kc-logo-text\"><span>Shared Realm</span></div>",
      "registrationAllowed": true,
      "registrationEmailAsUsername": false,
      "rememberMe": true,
      "verifyEmail": false,
      "loginWithEmailAllowed": true,
      "duplicateEmailsAllowed": false,
      "resetPasswordAllowed": true,
      "editUsernameAllowed": false,
      "bruteForceProtected": true,
      "permanentLockout": false,
      "maxFailureWaitSeconds": 900,
      "minimumQuickLoginWaitSeconds": 60,
      "waitIncrementSeconds": 60,
      "quickLoginCheckMilliSeconds": 1000,
      "maxDeltaTimeSeconds": 43200,
      "failureFactor": 30,
      "defaultRoles": ["default-roles-shared-realm", "offline_access", "uma_authorization"],
      "requiredCredentials": ["password"],
      "otpPolicyType": "totp",
      "otpPolicyAlgorithm": "HmacSHA1",
      "otpPolicyInitialCounter": 0,
      "otpPolicyDigits": 6,
      "otpPolicyLookAheadWindow": 1,
      "otpPolicyPeriod": 30,
      "browserSecurityHeaders": {
        "contentSecurityPolicyReportOnly": "",
        "xContentTypeOptions": "nosniff",
        "xRobotsTag": "none",
        "xFrameOptions": "SAMEORIGIN",
        "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';",
        "xXSSProtection": "1; mode=block",
        "strictTransportSecurity": "max-age=31536000; includeSubDomains"
      },
      "clients": [
        {
          "clientId": "cms-client",
          "name": "CMS Application",
          "enabled": true,
          "clientAuthenticatorType": "client-secret",
          "secret": "REPLACE_WITH_ACTUAL_SECRET",
          "redirectUris": ["*"],
          "webOrigins": ["*"],
          "protocol": "openid-connect",
          "attributes": {
            "saml.assertion.signature": "false",
            "saml.force.post.binding": "false",
            "saml.multivalued.roles": "false",
            "saml.encrypt": "false",
            "saml.server.signature": "false",
            "saml.server.signature.keyinfo.ext": "false",
            "exclude.session.state.from.auth.response": "false",
            "saml_force_name_id_format": "false",
            "saml.client.signature": "false",
            "tls.client.certificate.bound.access.tokens": "false",
            "saml.authnstatement": "false",
            "display.on.consent.screen": "false",
            "saml.onetimeuse.condition": "false"
          },
          "authenticationFlowBindingOverrides": {},
          "fullScopeAllowed": true,
          "nodeReRegistrationTimeout": -1,
          "defaultClientScopes": ["web-origins", "profile", "roles", "email"],
          "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]
        },
        {
          "clientId": "api-client",
          "name": "API Application",
          "enabled": true,
          "clientAuthenticatorType": "client-secret",
          "secret": "REPLACE_WITH_ACTUAL_SECRET",
          "standardFlowEnabled": false,
          "serviceAccountsEnabled": true,
          "protocol": "openid-connect",
          "attributes": {
            "exclude.session.state.from.auth.response": "false",
            "tls.client.certificate.bound.access.tokens": "false",
            "display.on.consent.screen": "false"
          },
          "authenticationFlowBindingOverrides": {},
          "fullScopeAllowed": true,
          "nodeReRegistrationTimeout": -1,
          "defaultClientScopes": ["web-origins", "profile", "roles", "email"],
          "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]
        },
        {
          "clientId": "frontend-client",
          "name": "Frontend Application",
          "enabled": true,
          "publicClient": true,
          "redirectUris": ["*"],
          "webOrigins": ["*"],
          "protocol": "openid-connect",
          "attributes": {
            "saml.assertion.signature": "false",
            "saml.force.post.binding": "false",
            "saml.multivalued.roles": "false",
            "saml.encrypt": "false",
            "saml.server.signature": "false",
            "saml.server.signature.keyinfo.ext": "false",
            "exclude.session.state.from.auth.response": "false",
            "saml_force_name_id_format": "false",
            "saml.client.signature": "false",
            "tls.client.certificate.bound.access.tokens": "false",
            "saml.authnstatement": "false",
            "display.on.consent.screen": "false",
            "saml.onetimeuse.condition": "false"
          },
          "authenticationFlowBindingOverrides": {},
          "fullScopeAllowed": true,
          "nodeReRegistrationTimeout": -1,
          "defaultClientScopes": ["web-origins", "profile", "roles", "email"],
          "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]
        }
      ]
    }
---
apiVersion: batch/v1
kind: Job
metadata:
  name: keycloak-db-init
  namespace: default
spec:
  template:
    spec:
      restartPolicy: OnFailure
      initContainers:
        - name: wait-for-postgres
          image: postgres:15-alpine
          env:
            - name: PGPASSWORD
              valueFrom:
                secretKeyRef:
                  name: postgres-secret
                  key: postgres-password
          command:
            - /bin/sh
            - -c
          args:
            - |
              until pg_isready -h postgres-service.default.svc.cluster.local -p 5432 -U admin; do
                echo "Waiting for PostgreSQL to be ready..."
                sleep 5
              done
              echo "PostgreSQL is ready!"
      containers:
        - name: db-init
          image: postgres:15-alpine
          env:
            - name: PGPASSWORD
              valueFrom:
                secretKeyRef:
                  name: postgres-secret
                  key: postgres-password
          command:
            - /bin/sh
            - -c
          args:
            - |
              psql -h postgres-service.default.svc.cluster.local -p 5432 -U admin -d postgres -c "CREATE DATABASE keycloak;" || echo "Database keycloak already exists"
              echo "Keycloak database initialized!"
