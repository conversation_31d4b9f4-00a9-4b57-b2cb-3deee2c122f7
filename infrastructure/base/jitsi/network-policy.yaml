apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: jitsi-network-policy
  namespace: jitsi-server
  labels:
    app.kubernetes.io/name: jitsi
spec:
  podSelector:
    matchLabels:
      app.kubernetes.io/name: jitsi
  policyTypes:
    - Ingress
    - Egress
  ingress:
    # Allow traffic from <PERSON>raefik in default namespace
    - from:
        - namespaceSelector:
            matchLabels:
              kubernetes.io/metadata.name: default
      ports:
        - protocol: TCP
          port: 80
        - protocol: TCP
          port: 443
        - protocol: TCP
          port: 5280  # For WebSocket/BOSH
        - protocol: TCP
          port: 8080  # For JVB WebSocket
    # Allow traffic from traefik-system namespace (if exists)
    - from:
        - namespaceSelector:
            matchLabels:
              name: traefik-system
      ports:
        - protocol: TCP
          port: 80
        - protocol: TCP
          port: 443
        - protocol: TCP
          port: 5280
        - protocol: TCP
          port: 8080
    # Allow internal communication between Jitsi components
    - from:
        - podSelector:
            matchLabels:
              app.kubernetes.io/name: jitsi
      ports:
        - protocol: TCP
          port: 5222  # Prosody XMPP
        - protocol: TCP
          port: 5280  # Prosody BOSH
        - protocol: TCP
          port: 5347  # Prosody Component
        - protocol: TCP
          port: 8080  # JVB HTTP
        - protocol: TCP
          port: 8888  # Jicofo HTTP
        - protocol: UDP
          port: 10000 # JVB RTP
    # Allow external UDP traffic to JVB
    - ports:
        - protocol: UDP
          port: 10000
  egress:
    # Allow DNS resolution
    - to: []
      ports:
        - protocol: UDP
          port: 53
        - protocol: TCP
          port: 53
    # Allow communication to PostgreSQL
    - to:
        - podSelector:
            matchLabels:
              app: postgres
      ports:
        - protocol: TCP
          port: 5432
    # Allow STUN/TURN servers
    - to: []
      ports:
        - protocol: UDP
          port: 443
        - protocol: TCP
          port: 443
    # Allow internal communication between Jitsi components
    - to:
        - podSelector:
            matchLabels:
              app.kubernetes.io/name: jitsi
      ports:
        - protocol: TCP
          port: 5222
        - protocol: TCP
          port: 5280
        - protocol: TCP
          port: 5347
        - protocol: TCP
          port: 8080
        - protocol: TCP
          port: 8888
        - protocol: UDP
          port: 10000
    # Allow outbound HTTPS for updates and external services
    - to: []
      ports:
        - protocol: TCP
          port: 443
        - protocol: TCP
          port: 80
