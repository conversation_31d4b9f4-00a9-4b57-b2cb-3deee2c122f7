apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: jitsi-nodeport-ingress
  namespace: jitsi-server
  annotations:
    traefik.ingress.kubernetes.io/router.entrypoints: web
    traefik.ingress.kubernetes.io/router.priority: "100"
spec:
  rules:
  - http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: jitsi-web-service
            port:
              number: 80
      - path: /http-bind
        pathType: Prefix
        backend:
          service:
            name: prosody-service
            port:
              number: 5280
