apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

metadata:
  name: jitsi-meet
  namespace: jitsi-server

resources:
  - jitsi-secrets.yaml
  - prosody-config.yaml
  - prosody.yaml
  - jicofo.yaml
  - jvb.yaml
  - jitsi-web.yaml
  - ingress.yaml
  - jvb-hpa.yaml
  - network-policy.yaml

commonLabels:
  app.kubernetes.io/name: jitsi
  app.kubernetes.io/instance: jitsi-meet
  app.kubernetes.io/version: "stable-8719"
  app.kubernetes.io/managed-by: kustomize

images:
  - name: jitsi/prosody
    newTag: stable-8719
  - name: jitsi/jicofo
    newTag: stable-8719
  - name: jitsi/jvb
    newTag: stable-8719
  - name: jitsi/web
    newTag: stable-8719

replicas:
  - name: prosody
    count: 1
  - name: jicofo
    count: 1
  - name: jitsi-videobridge
    count: 2
  - name: jitsi-web
    count: 2

patches:
  - target:
      kind: Deployment
      name: jitsi-videobridge
    patch: |-
      - op: replace
        path: /spec/template/spec/containers/0/env/1/value
        value: "AUTO"  # This should be replaced with actual public IP

configMapGenerator:
  - name: jitsi-environment-config
    literals:
      - DOMAIN=jitsi.osp.vn
      - TIMEZONE=Asia/Ho_Chi_Minh
      - ENABLE_GUESTS=1
      - ENABLE_LOBBY=1
      - ENABLE_AV_MODERATION=1
      - ENABLE_BREAKOUT_ROOMS=1
