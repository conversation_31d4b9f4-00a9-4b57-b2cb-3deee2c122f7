apiVersion: apps/v1
kind: Deployment
metadata:
  name: jicofo
  namespace: jitsi-server
  labels:
    app.kubernetes.io/name: jitsi
    app.kubernetes.io/component: jicofo
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: jitsi
      app.kubernetes.io/component: jicofo
  template:
    metadata:
      labels:
        app.kubernetes.io/name: jitsi
        app.kubernetes.io/component: jicofo
    spec:
      hostAliases:
        - ip: "************"
          hostnames:
            - "jitsi.osp.vn"
      containers:
        - name: jicofo
          image: jitsi/jicofo:stable-8719
          ports:
            - containerPort: 8888
              name: http
          env:
            - name: XMPP_SERVER
              value: "prosody-service"
            - name: XMPP_DOMAIN
              value: "jitsi.osp.vn"
            - name: XMPP_AUTH_DOMAIN
              value: "auth.jitsi.osp.vn"
            - name: XMPP_MUC_DOMAIN
              value: "muc.jitsi.osp.vn"
            - name: XMPP_INTERNAL_MUC_DOMAIN
              value: "internal-muc.jitsi.osp.vn"
            - name: XMPP_PORT
              value: "5222"
            - name: JICOFO_COMPONENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: jitsi-secret
                  key: jicofo-component-secret
            # Disable auth completely for anonymous mode
            - name: ENABLE_AUTH
              value: "0"
            - name: JICOFO_ENABLE_AUTH
              value: "false"
            - name: JICOFO_AUTH_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: jitsi-secret
                  key: jicofo-auth-password
            # Use anonymous domain (already defined above)
            - name: JICOFO_AUTH_TYPE
              value: "XMPP"
            - name: JICOFO_XMPP_DISABLE_TLS
              value: "true"
            - name: XMPP_USE_TLS
              value: "false"
            - name: XMPP_START_TLS
              value: "false"
            - name: JVB_BREWERY_MUC
              value: "jvbbrewery"
            - name: JICOFO_ENABLE_BRIDGE_HEALTH_CHECKS
              value: "true"
            - name: JICOFO_CONF_INITIAL_PARTICIPANT_WAIT_TIMEOUT
              value: "15000"
            - name: JICOFO_CONF_SINGLE_PARTICIPANT_TIMEOUT
              value: "20000"
            - name: JICOFO_ENABLE_HEALTH_CHECKS
              value: "true"
            - name: JICOFO_SHORT_ID
              value: "1"
            - name: JICOFO_RESERVATION_ENABLED
              value: "false"
            - name: JICOFO_RESERVATION_REST_BASE_URL
              value: "http://reservation.example.com"
            - name: TZ
              value: "Asia/Ho_Chi_Minh"
          resources:
            requests:
              memory: "768Mi"
              cpu: "300m"
            limits:
              memory: "2Gi"
              cpu: "1000m"
          # Disable health checks for debugging
          # livenessProbe:
          #   httpGet:
          #     path: /about/health
          #     port: 8888
          #   initialDelaySeconds: 60
          #   periodSeconds: 30
          #   timeoutSeconds: 10
          # readinessProbe:
          #   httpGet:
          #     path: /about/health
          #     port: 8888
          #   initialDelaySeconds: 30
          #   periodSeconds: 10
          #   timeoutSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: jicofo-service
  namespace: jitsi-server
  labels:
    app.kubernetes.io/name: jitsi
    app.kubernetes.io/component: jicofo
spec:
  ports:
    - port: 8888
      targetPort: 8888
      name: http
  selector:
    app.kubernetes.io/name: jitsi
    app.kubernetes.io/component: jicofo
  type: ClusterIP
