# Hướng dẫn truy cập Jitsi qua mạng LAN và Fix WebSocket

## Tổng quan

Tài liệu này hướng dẫn cách:
1. <PERSON><PERSON><PERSON> cập Jitsi Meet qua mạng LAN (Local Area Network)
2. Fix lỗi WebSocket connection cho Jitsi
3. Cấu hình Traefik để hỗ trợ WebSocket properly

## Cấu hình đã thực hiện

### 1. WebSocket Fix
- Thêm middleware `jitsi-websocket-headers` để xử lý WebSocket headers
- Cấu hình đúng routing cho `/xmpp-websocket` và `/colibri-ws/`
- Đảm bảo Prosody service expose port 5280 cho BOSH/WebSocket

### 2. LAN Access Support
- Tạo NodePort services cho truy cập trực tiếp
- Thêm IngressRoute cho LAN với regex pattern cho IP addresses
- Hỗ trợ cả `.local` domains và IP addresses

### 3. Services được tạo
- `jitsi-web-nodeport`: Port 30081 cho web interface
- `prosody-nodeport`: Port 30281 cho WebSocket/BOSH
- `jvb-udp-service`: Port 30000 cho media traffic (đã có sẵn)

## Cách truy cập

### 1. Truy cập qua Domain (jitsi.osp.vn)
```
http://jitsi.osp.vn:32032   (via Traefik HTTP NodePort)
https://jitsi.osp.vn:31576  (via Traefik HTTPS NodePort)
```
**Lưu ý**: Domain jitsi.osp.vn cần trỏ về ************

### 2. Truy cập LAN - Method 1: Via Traefik NodePort (Khuyến nghị)
```
http://************:32032    (master node)
http://************:32032    (warehouse01 node)
http://************:32032    (warehouse03 node)
```

### 3. Truy cập LAN - Method 2: Direct Jitsi NodePort
```
http://************:30081
http://************:30081
http://************:30081
```

### 4. Truy cập qua hostname local
```
http://master.local
http://warehouse01.local
http://warehouse03.local
```

## Deployment

### 1. Chuẩn bị
```bash
# Set KUBECONFIG
export KUBECONFIG=/Users/<USER>/code/projects/2025/k8s-deployment/.kube/config

# Make script executable
chmod +x deploy-lan-access.sh
```

### 2. Deploy
```bash
# Deploy với script
./deploy-lan-access.sh deploy

# Hoặc deploy manual
kubectl apply -f .
```

### 3. Kiểm tra status
```bash
./deploy-lan-access.sh status
```

### 4. Xem thông tin truy cập
```bash
./deploy-lan-access.sh info
```

## Troubleshooting

### 1. Kiểm tra WebSocket
```bash
# Test WebSocket connectivity
./deploy-lan-access.sh test

# Manual test với wscat
npm install -g wscat
wscat -c wss://jitsi.osp.vn/xmpp-websocket
```

### 2. Kiểm tra logs
```bash
# Xem logs của từng component
./deploy-lan-access.sh logs prosody
./deploy-lan-access.sh logs jicofo
./deploy-lan-access.sh logs jvb
./deploy-lan-access.sh logs web
```

### 3. Kiểm tra services
```bash
kubectl get svc -n jitsi-server
kubectl get ingressroute -n jitsi-server
```

### 4. Test internal connectivity
```bash
# Test từ bên trong cluster
kubectl exec -n jitsi-server deployment/prosody -- nc -zv prosody-service 5280
kubectl exec -n jitsi-server deployment/jitsi-web -- curl -I http://prosody-service:5280/http-bind
```

## Cấu hình Network

### 1. Firewall Rules (cần thiết cho LAN access)
```bash
# Trên các nodes, mở ports:
sudo ufw allow 32032/tcp   # Traefik HTTP NodePort
sudo ufw allow 31576/tcp   # Traefik HTTPS NodePort
sudo ufw allow 30080/tcp   # Direct Jitsi NodePort
sudo ufw allow 30000/udp   # NodePort for JVB media
sudo ufw allow 30280/tcp   # NodePort for WebSocket
```

### 2. DNS Configuration (tùy chọn)
Thêm vào `/etc/hosts` trên client machines:
```
************    jitsi.local master.local
************    warehouse01.local
************    warehouse03.local
```

## WebSocket Endpoints

### 1. XMPP WebSocket
```
Path: /xmpp-websocket
Service: prosody-service:5280
Usage: Client-to-server XMPP communication
```

### 2. Colibri WebSocket
```
Path: /colibri-ws/
Service: jvb-service:8080
Usage: Jitsi Videobridge WebSocket API
```

### 3. BOSH (HTTP Binding)
```
Path: /http-bind
Service: prosody-service:5280
Usage: HTTP binding for XMPP (fallback)
```

## Performance Tuning

### 1. JVB Configuration
- Memory: 2Gi request, 4Gi limit
- CPU: 1000m request, 2000m limit
- GC: G1GC with string deduplication

### 2. Prosody Configuration
- Memory: 512Mi request, 1Gi limit
- CPU: 200m request, 500m limit

### 3. Web Configuration
- Memory: 512Mi request, 1Gi limit
- CPU: 200m request, 500m limit

## Security Considerations

### 1. Network Policies
- Chỉ cho phép traffic từ Traefik namespace
- Cho phép internal communication giữa Jitsi components
- Cho phép external UDP traffic cho JVB

### 2. CORS Configuration
- Cho phép tất cả origins cho development
- Trong production nên restrict origins

### 3. Headers Security
- X-Frame-Options: SAMEORIGIN
- X-Content-Type-Options: nosniff
- Referrer-Policy: strict-origin-when-cross-origin

## Monitoring

### 1. Health Checks
- Prosody: TCP check on port 5222
- JVB: HTTP check on /about/health (disabled for debugging)
- Web: HTTP check on /health

### 2. Metrics
- JVB exposes metrics on port 8080
- Prosody có thể enable mod_prometheus

## Cleanup

```bash
# Remove all Jitsi resources
./deploy-lan-access.sh cleanup

# Manual cleanup
kubectl delete -f .
```

## Support

Nếu gặp vấn đề:
1. Kiểm tra logs với script
2. Verify network connectivity
3. Check firewall rules
4. Test WebSocket endpoints
5. Verify DNS resolution
