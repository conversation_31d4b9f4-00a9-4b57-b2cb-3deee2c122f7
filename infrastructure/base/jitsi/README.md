# Jitsi Meet Kubernetes Manifests

Th<PERSON> mục này chứa tất cả các manifest Kubernetes cần thiết để triển khai Ji<PERSON>i Meet trên cluster của bạn.

## Kiến trúc Jitsi Meet

<PERSON><PERSON><PERSON> Meet là một nền tảng hội nghị video mã nguồn mở bao gồm các thành phần chính sau:

### Sơ đồ kiến trúc
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Client Web    │    │   Client App    │    │   Client App    │
│   (Browser)     │    │   (Mobile/PC)   │    │   (Mobile/PC)   │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌────────────▼────────────┐
                    │     Traefik Ingress     │
                    │    (Load Balancer)      │
                    └────────────┬────────────┘
                                 │
                    ┌────────────▼────────────┐
                    │      Jitsi Web          │
                    │   (HTTP Interface)      │
                    └────────────┬────────────┘
                                 │
                    ┌────────────▼────────────┐
                    │       Prosody           │
                    │    (XMPP Server)        │
                    └────────────┬────────────┘
                                 │
                    ┌────────────▼────────────┐
                    │       Jicofo            │
                    │  (Conference Focus)     │
                    └────────────┬────────────┘
                                 │
                    ┌────────────▼────────────┐
                    │   Jitsi Videobridge     │
                    │    (Media Relay)        │
                    └─────────────────────────┘
```

### Các thành phần chính

#### 1. **Jitsi Web** (Frontend)
- **<PERSON><PERSON><PERSON> năng**: <PERSON>ia<PERSON> diện web để tham gia cuộc họp
- **Công nghệ**: React.js application
- **Port**: 80 (HTTP), 443 (HTTPS)
- **Giao thức**: HTTP/HTTPS, WebSocket
- **Expose ra ngoài**: ✅ Qua Traefik Ingress

#### 2. **Prosody** (XMPP Server)
- **Chức năng**: Máy chủ XMPP để quản lý signaling và authentication
- **Công nghệ**: Lua-based XMPP server
- **Ports**: 
  - 5222 (XMPP client-to-server)
  - 5280 (BOSH HTTP binding)
  - 5347 (XMPP component protocol)
- **Giao thức**: XMPP, HTTP (BOSH), WebSocket
- **Expose ra ngoài**: ✅ BOSH endpoint qua Ingress

#### 3. **Jicofo** (JItsi COnference FOcus)
- **Chức năng**: Quản lý conference lifecycle và bridge allocation
- **Công nghệ**: Java application
- **Port**: 8888 (HTTP API)
- **Giao thức**: XMPP (internal), HTTP REST API
- **Expose ra ngoài**: ❌ (Internal only)

#### 4. **JVB** (Jitsi VideoBridge)
- **Chức năng**: Media relay server để forward audio/video streams
- **Công nghệ**: Java application
- **Ports**:
  - 8080 (HTTP API - internal)
  - 10000 (UDP RTP - external)
- **Giao thức**: RTP/UDP, WebRTC, HTTP
- **Expose ra ngoài**: ✅ UDP port 10000 cho media traffic

### Giao thức liên lạc giữa các thành phần

1. **Client ↔ Jitsi Web**: HTTPS, WebSocket
2. **Jitsi Web ↔ Prosody**: BOSH (HTTP), WebSocket
3. **Prosody ↔ Jicofo**: XMPP component protocol
4. **Jicofo ↔ JVB**: XMPP, HTTP REST API
5. **Client ↔ JVB**: WebRTC (RTP over UDP), ICE/STUN/TURN

### Ports cần mở ra ngoài

#### **Ingress (Traefik)**
- **80/TCP**: HTTP redirect
- **443/TCP**: HTTPS traffic

#### **JVB Media Traffic**
- **10000/UDP**: RTP media streams (cần NodePort hoặc LoadBalancer)

## Tổng quan các file

| File                  | Mô tả                                          |
| --------------------- | ---------------------------------------------- |
| `jitsi-secrets.yaml`  | Secret chứa thông tin xác thực                 |
| `prosody-config.yaml` | ConfigMap cấu hình cho Prosody XMPP server     |
| `prosody.yaml`        | Deployment và service cho Prosody              |
| `jicofo.yaml`         | Deployment và service cho Jicofo               |
| `jvb.yaml`            | Deployment và service cho Jitsi Videobridge    |
| `jitsi-web.yaml`      | Deployment và service cho Web interface        |
| `ingress.yaml`        | Traefik IngressRoute cho truy cập từ bên ngoài |
| `jvb-hpa.yaml`        | Horizontal Pod Autoscaler cho JVB              |
| `network-policy.yaml` | Chính sách mạng cho bảo mật                    |
| `kustomization.yaml`  | Cấu hình Kustomize                             |
| `deploy-jitsi.sh`     | Script triển khai                              |

## Triển khai nhanh

### Sử dụng deployment script (Khuyến nghị):
```bash
# Cấp quyền thực thi cho script
chmod +x deploy-jitsi.sh

# Triển khai với domain tùy chỉnh
./deploy-jitsi.sh deploy meet.yourcompany.com

# Kiểm tra trạng thái
./deploy-jitsi.sh status

# Xem logs
./deploy-jitsi.sh logs
```

### Sử dụng kubectl trực tiếp:
```bash
# Thiết lập KUBECONFIG trước khi chạy kubectl
export KUBECONFIG=/Users/<USER>/code/projects/2025/k8s-deployment/.kube/config

# Áp dụng tất cả manifests
kubectl apply -f .

# Kiểm tra trạng thái deployment
kubectl get pods -l app.kubernetes.io/name=jitsi

# Kiểm tra services
kubectl get services -l app.kubernetes.io/name=jitsi
```

### Sử dụng Kustomize:
```bash
# Thiết lập KUBECONFIG trước khi chạy kubectl
export KUBECONFIG=/Users/<USER>/code/projects/2025/k8s-deployment/.kube/config

# Triển khai sử dụng kustomize
kubectl apply -k .

# Hoặc build và apply
kustomize build . | kubectl apply -f -
```

## Cấu hình trước khi triển khai

### 1. Cập nhật Domain
Domain hiện tại đã được cấu hình là `jitsi.osp.vn`. Nếu cần thay đổi domain khác:
- Sử dụng script `deploy-jitsi.sh` với tham số `DOMAIN` để tự động cập nhật tất cả các file
- Hoặc thủ công cập nhật trong các file: `ingress.yaml`, `jitsi-web.yaml`, `prosody.yaml`, `jicofo.yaml`, `jvb.yaml`

### 2. Cập nhật Secrets (QUAN TRỌNG!)
File `jitsi-secrets.yaml` chứa mật khẩu mặc định đã được encode base64. **Bạn PHẢI thay đổi những giá trị này trong môi trường production!**

Tạo secrets mới:
```bash
echo -n "your-new-password" | base64
```

Thay thế các giá trị trong `jitsi-secrets.yaml` bằng secrets bạn đã tạo.

### 3. Cấu hình Public IP cho JVB
Cập nhật `DOCKER_HOST_ADDRESS` trong `jvb.yaml` bằng địa chỉ IP public của cluster.

## Yêu cầu tài nguyên

| Thành phần | CPU Request | Memory Request | CPU Limit | Memory Limit |
| ---------- | ----------- | -------------- | --------- | ------------ |
| Prosody    | 200m        | 512Mi          | 500m      | 1Gi          |
| Jicofo     | 300m        | 768Mi          | 1000m     | 2Gi          |
| JVB        | 1000m       | 2Gi            | 2000m     | 4Gi          |
| Web        | 200m        | 512Mi          | 500m      | 1Gi          |

## Mạng (Networking)

### Ports nội bộ
- Prosody: 5222 (XMPP), 5280 (BOSH), 5347 (Component)
- Jicofo: 8888 (HTTP API)
- JVB: 8080 (HTTP API), 10000 (UDP RTP)
- Web: 80 (HTTP), 443 (HTTPS)

### Truy cập từ bên ngoài
- Web Interface: https://your-domain.com
- XMPP WebSocket: https://your-domain.com/xmpp-websocket
- Colibri WebSocket: https://your-domain.com/colibri-ws/

## Mở rộng (Scaling)

### JVB Auto-scaling
HPA được cấu hình để mở rộng JVB instances dựa trên:
- CPU utilization: 70%
- Memory utilization: 80%
- Min replicas: 2
- Max replicas: 10

### Mở rộng thủ công
```bash
# Thiết lập KUBECONFIG
export KUBECONFIG=/Users/<USER>/code/projects/2025/k8s-deployment/.kube/config

# Mở rộng JVB
kubectl scale deployment jitsi-videobridge --replicas=5

# Mở rộng Web interface
kubectl scale deployment jitsi-web --replicas=3
```

## Giám sát (Monitoring)

### Health Checks
Tất cả các thành phần đều có cấu hình liveness và readiness probes.

### Logs
```bash
# Thiết lập KUBECONFIG
export KUBECONFIG=/Users/<USER>/code/projects/2025/k8s-deployment/.kube/config

# Xem tất cả Jitsi logs
kubectl logs -l app.kubernetes.io/name=jitsi --tail=100

# Xem logs của từng thành phần
kubectl logs -l app.kubernetes.io/component=jvb
kubectl logs -l app.kubernetes.io/component=prosody
kubectl logs -l app.kubernetes.io/component=jicofo
kubectl logs -l app.kubernetes.io/component=web
```

## Xử lý sự cố (Troubleshooting)

### Các vấn đề thường gặp

1. **Pods không khởi động**: Kiểm tra tài nguyên có sẵn và capacity của nodes
2. **Lỗi xác thực**: Xác minh secrets được cấu hình đúng
3. **Kết nối mạng**: Kiểm tra service discovery và network policies
4. **Truy cập từ bên ngoài**: Xác minh cấu hình Traefik và DNS records

### Debug Commands
```bash
# Thiết lập KUBECONFIG
export KUBECONFIG=/Users/<USER>/code/projects/2025/k8s-deployment/.kube/config

# Kiểm tra trạng thái pod
kubectl describe pod <pod-name>

# Kiểm tra service endpoints
kubectl get endpoints

# Test kết nối nội bộ
kubectl exec -it <prosody-pod> -- nc -zv jicofo-service 8888

# Kiểm tra Traefik routes
kubectl get ingressroute
```

### Port Forward để test
```bash
# Thiết lập KUBECONFIG
export KUBECONFIG=/Users/<USER>/code/projects/2025/k8s-deployment/.kube/config

# Truy cập Jitsi Web locally
kubectl port-forward service/jitsi-web-service 8080:80

# Truy cập Prosody BOSH
kubectl port-forward service/prosody-service 5280:5280
```

## Các cân nhắc về bảo mật

1. **Thay đổi default secrets** trước khi triển khai production
2. **Cấu hình network policies** để hạn chế traffic không cần thiết
3. **Sử dụng TLS/SSL** cho tất cả communications bên ngoài
4. **Cập nhật thường xuyên** container images
5. **Giám sát access logs** để phát hiện hoạt động đáng ngờ

## Tích hợp với hạ tầng hiện có

### Tích hợp PostgreSQL
Triển khai này được thiết kế để hoạt động với hạ tầng PostgreSQL hiện có. Không cần cấu hình database bổ sung vì các thành phần Jitsi sử dụng internal storage mặc định.

### Tích hợp Keycloak SSO
Để tích hợp SSO với Keycloak, cần cấu hình bổ sung:
1. Cấu hình JWT authentication trong Jitsi
2. Thiết lập OIDC client trong Keycloak
3. Cập nhật cấu hình Prosody cho external authentication

### Tích hợp Monitoring
Để tích hợp với monitoring hiện có:
1. Thêm Jitsi metrics vào Prometheus targets
2. Tạo Grafana dashboards
3. Cấu hình alerts trong AlertManager

## Dọn dẹp (Cleanup)

### Sử dụng script:
```bash
./deploy-jitsi.sh cleanup
```

### Dọn dẹp thủ công:
```bash
# Thiết lập KUBECONFIG
export KUBECONFIG=/Users/<USER>/code/projects/2025/k8s-deployment/.kube/config

kubectl delete -f .
# Hoặc sử dụng kustomize
kubectl delete -k .
```

---

Để biết thêm thông tin chi tiết, tham khảo file [README.md](../../../README.md) chính.
