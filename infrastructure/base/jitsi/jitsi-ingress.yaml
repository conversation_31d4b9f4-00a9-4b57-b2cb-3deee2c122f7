apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: jitsi-ingress
  namespace: jitsi-server
  annotations:
    traefik.ingress.kubernetes.io/router.entrypoints: web,websecure
    traefik.ingress.kubernetes.io/router.tls: "true"
    cert-manager.io/cluster-issuer: letsencrypt-staging
spec:
  tls:
  - hosts:
    - jitsi.osp.vn
    secretName: jitsi-tls-cert
  rules:
  - host: jitsi.osp.vn
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: jitsi-web-service
            port:
              number: 80
      - path: /http-bind
        pathType: Prefix
        backend:
          service:
            name: prosody-service
            port:
              number: 5280
