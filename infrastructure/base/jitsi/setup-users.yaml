apiVersion: batch/v1
kind: Job
metadata:
  name: prosody-setup-users
  namespace: jitsi-server
  labels:
    app.kubernetes.io/name: jitsi
    app.kubernetes.io/component: setup
spec:
  template:
    spec:
      containers:
      - name: prosody-setup
        image: jitsi/prosody:stable-8719
        command: ["/bin/bash"]
        args:
          - -c
          - |
            set -e
            echo "Waiting for Prosody to be ready..."
            sleep 30
            
            echo "Creating users..."
            # Connect to Prosody service and create users
            prosodyctl --config=/etc/prosody/prosody.cfg.lua register focus auth.jitsi.osp.vn focuspassword || true
            prosodyctl --config=/etc/prosody/prosody.cfg.lua register jvb auth.jitsi.osp.vn jvbpassword || true
            
            echo "Users created successfully"
        env:
        - name: PROSODY_SERVER
          value: "prosody-service"
        - name: XMPP_DOMAIN
          value: "jitsi.osp.vn"
        - name: XMPP_AUTH_DOMAIN
          value: "auth.jitsi.osp.vn"
        volumeMounts:
        - name: prosody-config
          mountPath: /etc/prosody/prosody.cfg.lua
          subPath: prosody.cfg.lua
      volumes:
      - name: prosody-config
        configMap:
          name: prosody-config
          items:
          - key: prosody.cfg.lua
            path: prosody.cfg.lua
      restartPolicy: OnFailure
  backoffLimit: 3
