apiVersion: v1
kind: Secret
metadata:
  name: prosody-tls
  namespace: jitsi-server
type: Opaque
data:
  # Self-signed certificate for internal communication (base64 encoded)
  tls.crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURrekNDQW51Z0F3SUJBZ0lKQUk3YmpIOTZtdkhZTUEwR0NTcUdTSWIzRFFFQkN3VUFNRzR4Q3pBSkJnTlYKQkFZVEFsVlRNUXN3Q1FZRFZRUUlEQUpEUVRFVE1CRUdBMVVFQnd3S1UyRnVJRVp5WVc1amFYTmpiekVOTUFzRwpBMVVFQ2d3RVNtbDBjMmt4RXpBUkJnTlZCQXNNQ2xSbGMzUWdVMlZ5ZG1WeU1SOHdIUVlEVlFRRERCWndjbTl6CmIyUjVMWE5sY25acFkyVXVaR1ZtWVhWc2RERXdIaGNOTWpVd056STFNRE16TmpJMFdoY05Nall3TnpJMU1ETnoKTmpJMFdqQnVNUXN3Q1FZRFZRUUdFd0pWVXpFTE1Ba0dBMVVFQ0F3Q1EwRXhFekFSQmdOVkJBY01DbE5oYmlCRwpjbUZ1WTJselkyOHhEVEFMQmdOVkJBb01CRXBwZEhOcE1STXdFUVlEVlFRTERBcFVaWE4wSUZObGNuWmxjakVmCk1CMEdBMVVFQXd3V2NISnZjMjlrZVMxelpYSjJhV05sTG1SbFptRjFiSFF4TUlJQklqQU5CZ2txaGtpRzl3MEIKQVFFRkFBT0NBUThBTUlJQkNnS0NBUUVBdGE5K3JTcUE4VkUyQVBWMHN4SU1QMVFPSDFyYUxZSEpJV1pHQjBOdgpWZ1lsY3NhSjJLZ3N4ZU5oOTNIQUw4YzJsT0Z4QVJYblliWGFqZWFqajZnZlZYeERvUmNKZUtneU1MOFBEQVdqCkpBYVhjQWQ1SzB5K1ZBSGtqOHJ0VmM5WGZ0eWNWdFc5NEhQRGZhUmVnS1NoWEZNVzhnRm11amZkNlVyZVZvRkEKWm04K3gxRGNBcE5zekc1NVRQQ0k4SEUrTGdSTlRyVXIzNGhFaEpWa2xYUDE4TTZHQ1NTZFEzT3NGS3ZyOGE0NQovcG5oTE5RaU0wcFNjWU0zUXRKZ0J2QlJOa0xRS2k3eThCaXkxa1lHaUlZZnZrZStnUElsRGZaSzV3bG5tcE5BCkZiMGZva05KcGlKb1FyMDF0NjZqTmN0VVlpKzlEUU5DMTZONm5jSFdTWkk5MndJREFRQUJvMU13VVRBZEJnTlYKSFE0RUZnUVVtV1RGd3F5NUN3L0xnMzV1Qkk1S3JTZlpSYXd3SHdZRFZSMGpCQmd3Rm9BVW1XVEYKCi0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0=
  tls.key: 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
