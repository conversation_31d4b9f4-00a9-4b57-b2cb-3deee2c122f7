# Kubernetes Deployment Scripts

Bộ script triển khai các dịch vụ cơ bản trên Kubernetes bao gồm:

- **Traefik** - API Gateway với basic authentication cho dashboard
- **PostgreSQL** - Database với account admin tự động tạo
- **MinIO** - Object Storage với bucket riêng cho từng môi trường
- **Keycloak** - SSO Service với realm và client được cấu hình sẵn

## 📋 BÁNG CÁO TRIỂN KHAI - 24/07/2025

### ✅ Trạng thái triển khai

| Dịch vụ        | Trạng thái  | Pods Ready | Ghi chú                               |
| -------------- | ----------- | ---------- | ------------------------------------- |
| **Traefik**    | ✅ Hoạt động | 1/1        | API Gateway đã sẵn sàng               |
| **PostgreSQL** | ✅ Hoạt động | 1/1        | Database đã khởi tạo thành công       |
| **MinIO**      | ✅ Hoạt động | 1/1        | Object Storage và buckets đã được tạo |
| **Keycloak**   | ✅ Hoạt động | 1/1        | SSO service đã sẵn sàng               |
| **pgAdmin**    | ✅ Hoạt động | 1/1        | Database management tool đã sẵn sàng  |

### 🔧 Thông tin kết nối

#### 🌐 Cluster Information
- **Kubernetes API**: `https://************:6443`
- **Cluster Node**: `************`

#### 📱 Truy cập các dịch vụ

##### 🛡️ Traefik (API Gateway)
- **Dashboard URL**: `http://************:32032/dashboard/` (HTTP direct access)
- **API URL**: `http://************:32032/api/overview` (API endpoint)
- **Production URL**: `https://traefik.osp.vn` (domain-based access)
- **Username**: `admin`
- **Password**: `admin123`
- **Service Type**: LoadBalancer (NodePort fallback)
- **NodePorts**: HTTP:32032, HTTPS:31576

##### 🐘 PostgreSQL Database
- **Host**: `postgres-service.default.svc.cluster.local:5432` (internal)
- **External Access**: Không có (chỉ internal)
- **Username**: `admin`
- **Password**: `admin123`
- **Databases**:
  - `defaultdb` - Database mặc định
  - `keycloak` - Database cho Keycloak
  - `dev`, `staging`, `prod` - Databases cho các môi trường

##### 📦 MinIO Object Storage
- **Console URL**: `http://************:32032/minio-console/` (HTTP direct access)
- **API URL**: `http://************:32032/minio-api/` (HTTP direct access)
- **Production Console**: `https://minio-dev.osp.vn` (domain-based access)
- **Production API**: `https://minio-api-dev.osp.vn` (domain-based access)
- **Access Key**: `admin`
- **Secret Key**: `admin123`
- **Ports**: Console:9001, API:9000
- **Buckets**:
  - `dev-bucket` - Bucket cho môi trường dev
  - `staging-bucket` - Bucket cho môi trường staging
  - `prod-bucket` - Bucket cho môi trường production

##### 🔐 Keycloak SSO
- **URL**: `http://************:32032/keycloak/` (HTTP direct access)
- **Production URL**: `https://keycloak-dev.osp.vn` (domain-based access)
- **Admin Username**: `admin`
- **Admin Password**: `admin123`
- **Port**: 8080
- **Status**: ✅ Sẵn sàng sử dụng
- **Admin Console**: Truy cập qua Traefik routing

##### 🗄️ pgAdmin (Database Management)
- **URL**: `http://************:32032/pgadmin/` (HTTP direct access)
- **Production URL**: `https://pgadmin-dev.osp.vn` (domain-based access)
- **Email**: `<EMAIL>`
- **Password**: `admin123`
- **Port**: 80
- **PostgreSQL Connection**: Đã được cấu hình sẵn cho database CMS
- **Status**: ✅ Sẵn sàng sử dụng

#### 🔗 Service Access Methods

##### 📱 Direct IP Access (HTTP - Immediate Access)
*Truy cập trực tiếp qua IP cluster mà không cần cấu hình DNS*

- **Traefik Dashboard**: `http://************:32032/dashboard/`
- **Traefik API**: `http://************:32032/api/overview`
- **MinIO Console**: `http://************:32032/minio-console/`
- **MinIO API**: `http://************:32032/minio-api/`
- **Keycloak**: `http://************:32032/keycloak/`
- **pgAdmin**: `http://************:32032/pgadmin/`

##### 🌐 Domain-based Access (HTTPS - Production)
*(Cần cấu hình DNS hoặc hosts file để trỏ về ************)*

- **Traefik Dashboard Dev**: `https://traefik-dev.osp.vn`
- **MinIO Console Dev**: `https://minio-dev.osp.vn`
- **MinIO API Dev**: `https://minio-api-dev.osp.vn`
- **Keycloak Dev**: `https://keycloak-dev.osp.vn`
- **pgAdmin Dev**: `https://pgadmin-dev.osp.vn`

### 📊 Resources đã triển khai

#### ✅ Namespaces
- `default` - Chứa các shared services
- `cms-dev` - Môi trường development cho dự án CMS
- `cms-staging` - Môi trường staging cho dự án CMS (đã tạo)
- `cms-prod` - Môi trường production cho dự án CMS (đã tạo)

#### ✅ Secrets
- `postgres-secret` - Credentials cho PostgreSQL
- `minio-secret` - Credentials cho MinIO
- `keycloak-secret` - Credentials cho Keycloak admin
- `pgadmin-secret` - Credentials cho pgAdmin
- `traefik-dashboard-auth` - Basic auth cho Traefik dashboard

#### ✅ Services
- `traefik` (LoadBalancer)
- `traefik-dashboard` (ClusterIP)
- `postgres-service` (ClusterIP)
- `minio-api-service` (ClusterIP)
- `minio-console-service` (ClusterIP)
- `keycloak-service` (ClusterIP)
- `pgadmin-service` (ClusterIP)

#### ✅ IngressRoutes
- `traefik-dashboard` (default namespace)
- `traefik-dashboard-cms-dev` (cms-dev namespace)
- `minio-console-cms-dev` (cms-dev namespace)
- `minio-api-cms-dev` (cms-dev namespace)
- `keycloak-cms-dev` (cms-dev namespace)
- `pgadmin-cms-dev` (cms-dev namespace)

### 🔧 Các vấn đề đã khắc phục

1. **Traefik CRDs**: Đã cài đặt Traefik CRDs từ v3.0
2. **Traefik RBAC**: Đã cập nhật ClusterRole với đầy đủ quyền cho `traefik.io` API group
3. **Traefik Ping**: Đã thêm ping endpoint cho health checks
4. **API Version**: Đã sửa tất cả IngressRoute từ `traefik.containo.us` thành `traefik.io`
5. **Secrets**: Đã tạo tất cả secrets cần thiết cho các services
6. **Keycloak Config**: Đã chuyển sang `start-dev` mode và tăng health check timeout
7. **Traefik 404 Errors**: Đã khắc phục lỗi 404 khi truy cập services qua Traefik
   - Thêm HTTP-based ingress routes cho direct IP access
   - Cấu hình path-based routing với middleware strip prefix
   - Bật insecure API cho Traefik dashboard
   - Loại bỏ automatic HTTP to HTTPS redirect
8. **Port Conflicts**: Đã xác nhận KHÔNG có xung đột port giữa các services
   - MinIO: ports 9000 (API), 9001 (Console)
   - Keycloak: port 8080
   - Traefik: ports 80, 443 (external), 8080 (dashboard internal)

### ⚠️ Lưu ý quan trọng

1. **Keycloak**: Đang trong quá trình khởi tạo database schema, cần thời gian từ 5-10 phút
2. **TLS Certificates**: Cần cấu hình domain thực tế để Let's Encrypt hoạt động
3. **External Access**: Hiện tại truy cập qua NodePort, cần LoadBalancer thực tế cho production
4. **Security**: Tất cả passwords hiện tại là `admin123` - cần thay đổi trong production

### 🚀 Bước tiếp theo

1. ✅ **Hoàn tất**: Tất cả services đã triển khai thành công
2. Cấu hình domain names thực tế
3. Thiết lập TLS certificates cho HTTPS
4. Tạo realm và clients trong Keycloak
5. Triển khai môi trường staging và production

### 🎉 Tóm tắt triển khai thành công

**Đã triển khai thành công các dịch vụ sau:**
- ✅ **Traefik v3.0** - API Gateway với dashboard (domain: osp.vn)
- ✅ **PostgreSQL 15** - Database với 4 databases (defaultdb, keycloak, dev, staging, prod)
- ✅ **MinIO** - Object Storage với 3 buckets (dev-bucket, staging-bucket, prod-bucket)  
- ✅ **Keycloak 23.0** - SSO Service trong development mode
- ✅ **pgAdmin 4** - Database management tool với PostgreSQL pre-configured

**Thời gian triển khai**: ~20 phút  
**Pods running**: 5/5 services chính + 2 init jobs hoàn thành  
**Services**: 7 ClusterIP + 1 LoadBalancer  
**Ingress Routes**: 6 routes (1 default + 5 cms-dev environment)  
**Domain**: osp.vn với các subdomain cho từng service

### 📋 Lệnh kiểm tra hữu ích

```bash
# Kiểm tra trạng thái pods
kubectl get pods -n default
kubectl get pods -n dev

# Kiểm tra services
kubectl get svc -n default

# Kiểm tra ingress routes
kubectl get ingressroute -A

# Kiểm tra logs
kubectl logs -f keycloak-0 -n default
kubectl logs -f traefik-57f6fb55bb-tm7c9 -n default

# Truy cập PostgreSQL
kubectl exec -it postgres-0 -- psql -U admin -d defaultdb

# Kiểm tra MinIO buckets
kubectl exec -it minio-0 -- mc ls local/
```

## Cấu trúc thư mục

```
infrastructure/
├── base/                          # Các deployment cơ bản
│   ├── traefik.yaml              # Traefik deployment
│   ├── traefik-dashboard-ingress.yaml
│   ├── postgres.yaml             # PostgreSQL deployment
│   ├── minio.yaml                # MinIO deployment
│   └── keycloak.yaml             # Keycloak deployment
├── environments/                  # Cấu hình theo môi trường
│   ├── dev/
│   │   ├── ingress.yaml          # Ingress routes cho dev
│   │   └── secrets/              # Secrets cho dev
│   ├── staging/
│   └── prod/
├── scripts/                       # Scripts triển khai
│   ├── deploy.ps1                # Script triển khai đầy đủ
│   ├── quick-deploy.ps1          # Script triển khai nhanh
│   └── cleanup.ps1               # Script dọn dẹp
└── credentials-{env}.txt          # File chứa thông tin đăng nhập
```

## Cách sử dụng

### 1. Triển khai nhanh

```powershell
# Triển khai môi trường dev
.\infrastructure\scripts\quick-deploy.ps1 -Environment dev

# Triển khai môi trường staging
.\infrastructure\scripts\quick-deploy.ps1 -Environment staging

# Triển khai môi trường production
.\infrastructure\scripts\quick-deploy.ps1 -Environment prod
```

### 2. Triển khai đầy đủ (với nhiều tùy chọn hơn)

```powershell
# Triển khai với namespace tùy chỉnh
.\infrastructure\scripts\deploy.ps1 -Environment dev -Namespace my-dev

# Chỉ xem preview không thực hiện
.\infrastructure\scripts\deploy.ps1 -Environment dev -DryRun
```

### 3. Triển khai manual (đã thực hiện thành công)

```bash
# Thiết lập KUBECONFIG
export KUBECONFIG=/path/to/k8s-deployment/.kube/config

# Cài đặt Traefik CRDs
kubectl apply -f https://raw.githubusercontent.com/traefik/traefik/v3.0/docs/content/reference/dynamic-configuration/kubernetes-crd-definition-v1.yml

# Tạo secrets
kubectl create secret generic postgres-secret \
  --from-literal=postgres-user=admin \
  --from-literal=postgres-password=admin123

kubectl create secret generic minio-secret \
  --from-literal=accesskey=admin \
  --from-literal=secretkey=admin123

kubectl create secret generic keycloak-secret \
  --from-literal=admin-username=admin \
  --from-literal=admin-password=admin123

kubectl create secret generic traefik-dashboard-auth \
  --from-file=users=<(htpasswd -nb admin admin123)

# Triển khai base services
kubectl apply -f infrastructure/base/

# Tạo namespace dev và triển khai
kubectl create namespace dev
kubectl apply -f infrastructure/environments/dev/
```

### 4. Dọn dẹp

```powershell
# Xóa một môi trường cụ thể
.\infrastructure\scripts\cleanup.ps1 -Environment dev

# Xóa tất cả môi trường
.\infrastructure\scripts\cleanup.ps1 -Environment all

# Xóa cả shared services
.\infrastructure\scripts\cleanup.ps1 -Environment all -IncludeSharedServices
```

## Cấu hình Domain

Domain đã được cấu hình sử dụng **osp.vn**:

1. **base/traefik.yaml** - Email Let's Encrypt: `<EMAIL>`
2. **environments/*/ingress.yaml** - Subdomain pattern:
   - `traefik-{env}.osp.vn` - Traefik dashboard
   - `minio-{env}.osp.vn` - MinIO console
   - `minio-api-{env}.osp.vn` - MinIO API
   - `keycloak-{env}.osp.vn` - Keycloak
   - `pgadmin-{env}.osp.vn` - pgAdmin

**Lưu ý**: Cần cấu hình DNS records cho các subdomain trỏ về IP cluster: `************`

## URLs sau khi triển khai

### Development
- Traefik Dashboard: `https://traefik-dev.osp.vn`
- MinIO Console: `https://minio-dev.osp.vn`
- MinIO API: `https://minio-api-dev.osp.vn`
- Keycloak: `https://keycloak-dev.osp.vn`
- pgAdmin: `https://pgadmin-dev.osp.vn`

### Staging
- Traefik Dashboard: `https://traefik-staging.osp.vn`
- MinIO Console: `https://minio-staging.osp.vn`
- MinIO API: `https://minio-api-staging.osp.vn`
- Keycloak: `https://keycloak-staging.osp.vn`
- pgAdmin: `https://pgadmin-staging.osp.vn`

### Production
- Traefik Dashboard: `https://traefik.osp.vn`
- MinIO Console: `https://minio.osp.vn`
- MinIO API: `https://minio-api.osp.vn`
- Keycloak: `https://keycloak.osp.vn`
- pgAdmin: `https://pgadmin.osp.vn`

## Thông tin Database

- **PostgreSQL**: Chỉ có 1 instance dùng chung, các database riêng:
  - Database `dev` cho môi trường dev
  - Database `staging` cho môi trường staging
  - Database `prod` cho môi trường production
  - Database `keycloak` cho Keycloak

- **MinIO**: Chỉ có 1 instance dùng chung, các bucket riêng:
  - Bucket `dev-bucket` cho môi trường dev
  - Bucket `staging-bucket` cho môi trường staging
  - Bucket `prod-bucket` cho môi trường production

- **Keycloak**: 1 realm `shared-realm` dùng chung với các client:
  - `cms-client` - Cho CMS application
  - `api-client` - Cho API services  
  - `frontend-client` - Cho frontend application

## Kiểm tra trạng thái

```powershell
# Kiểm tra tất cả pods
kubectl get pods -n default
kubectl get pods -n dev
kubectl get pods -n staging
kubectl get pods -n prod

# Kiểm tra services
kubectl get svc -n default

# Kiểm tra ingress routes
kubectl get ingressroute -A

# Xem logs
kubectl logs -n default deployment/traefik
kubectl logs -n default statefulset/postgres
kubectl logs -n default statefulset/minio
kubectl logs -n default statefulset/keycloak
```

## Lưu ý bảo mật

1. **Passwords**: Tất cả passwords được tạo ngẫu nhiên và lưu trong file `credentials-{env}.txt`
2. **Secrets**: Kubernetes secrets được tạo tự động cho mỗi môi trường
3. **TLS**: Traefik tự động tạo certificates từ Let's Encrypt
4. **Basic Auth**: Traefik dashboard được bảo vệ bằng basic authentication

## Troubleshooting

### 1. Pods không start được
```powershell
kubectl describe pod <pod-name> -n <namespace>
kubectl logs <pod-name> -n <namespace>
```

### 2. Ingress không hoạt động
```powershell
kubectl get ingressroute -A
kubectl describe ingressroute <route-name> -n <namespace>
```

### 3. Database connection issues
```powershell
kubectl exec -it postgres-0 -n default -- psql -U admin -d postgres
```

### 4. MinIO bucket issues
```powershell
kubectl exec -it minio-0 -n default -- mc ls local/
```

### 5. Traefik 404 errors
```powershell
# Check if ingress routes are properly configured
kubectl get ingressroute -A

# Check Traefik logs
kubectl logs -l app=traefik -n default

# Test direct service access
kubectl port-forward svc/minio-console-service 9001:9001 -n default
curl http://localhost:9001

# Test HTTP access via Traefik
curl http://************:32032/minio-console/
```

### 6. Service accessibility verification
```powershell
# Test all services via HTTP direct access
curl -I http://************:32032/dashboard/
curl -I http://************:32032/minio-console/
curl -I http://************:32032/keycloak/
curl -I http://************:32032/pgadmin/

# Test with authentication for Traefik dashboard
curl -I -u admin:admin123 http://************:32032/dashboard/
```
