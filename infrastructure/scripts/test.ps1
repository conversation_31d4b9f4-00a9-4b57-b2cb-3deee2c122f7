#!/usr/bin/env pwsh

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("dev", "staging", "prod")]
    [string]$Environment
)

Write-Host "🧪 Testing $Environment environment services..." -ForegroundColor Green

$namespace = $Environment

Write-Host "`n📊 Checking pod status..." -ForegroundColor Blue
kubectl get pods -n default | Where-Object { $_ -match "(postgres|minio|keycloak|traefik)" }
kubectl get pods -n $namespace

Write-Host "`n🌐 Checking services..." -ForegroundColor Blue
kubectl get svc -n default | Where-Object { $_ -match "(postgres|minio|keycloak|traefik)" }

Write-Host "`n🔗 Checking ingress routes..." -ForegroundColor Blue
kubectl get ingressroute -n $namespace

Write-Host "`n🔐 Checking secrets..." -ForegroundColor Blue
kubectl get secrets -n $namespace
kubectl get secrets -n default | Where-Object { $_ -match "traefik-dashboard-auth" }

Write-Host "`n📋 Service endpoints:" -ForegroundColor Yellow
Write-Host "- Traefik Dashboard: https://traefik-$Environment.yourdomain.com" -ForegroundColor Cyan
Write-Host "- MinIO Console: https://minio-$Environment.yourdomain.com" -ForegroundColor Cyan
Write-Host "- MinIO API: https://minio-api-$Environment.yourdomain.com" -ForegroundColor Cyan
Write-Host "- Keycloak: https://keycloak-$Environment.yourdomain.com" -ForegroundColor Cyan

Write-Host "`n🔍 To check credentials, see:" -ForegroundColor Yellow
Write-Host "credentials-$Environment.txt" -ForegroundColor Cyan

Write-Host "`n🐛 Debug commands:" -ForegroundColor Blue
Write-Host "kubectl logs -n default deployment/traefik --tail=50" -ForegroundColor Gray
Write-Host "kubectl logs -n default statefulset/postgres --tail=50" -ForegroundColor Gray
Write-Host "kubectl logs -n default statefulset/minio --tail=50" -ForegroundColor Gray
Write-Host "kubectl logs -n default statefulset/keycloak --tail=50" -ForegroundColor Gray

Write-Host "`n✅ Health check completed!" -ForegroundColor Green
