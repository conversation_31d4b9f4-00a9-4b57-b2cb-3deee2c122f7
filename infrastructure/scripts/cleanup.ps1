#!/usr/bin/env pwsh

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("dev", "staging", "prod", "all")]
    [string]$Environment,
    
    [Parameter(Mandatory=$false)]
    [switch]$IncludeSharedServices
)

if ($Environment -eq "all") {
    Write-Host "🗑️ Removing all environments..." -ForegroundColor Red
    @("dev", "staging", "prod") | ForEach-Object {
        Write-Host "Removing namespace: $_" -ForegroundColor Yellow
        kubectl delete namespace $_ --ignore-not-found=true
    }
} else {
    Write-Host "🗑️ Removing $Environment environment..." -ForegroundColor Red
    kubectl delete namespace $Environment --ignore-not-found=true
}

if ($IncludeSharedServices) {
    Write-Host "🗑️ Removing shared services..." -ForegroundColor Red
    kubectl delete -f "$PSScriptRoot\..\base\" --ignore-not-found=true
}

Write-Host "✅ Cleanup completed!" -ForegroundColor Green
