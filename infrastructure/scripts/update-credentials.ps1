#!/usr/bin/env pwsh

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("dev", "staging", "prod")]
    [string]$Environment,
    
    [Parameter(Mandatory=$false)]
    [string]$Service = "all"
)

Write-Host "🔄 Updating credentials for $Environment environment..." -ForegroundColor Green

function New-RandomPassword {
    param([int]$Length = 16)
    $chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    return -join ((1..$Length) | ForEach {$chars[(Get-Random -Maximum $chars.Length)]})
}

$newCredentials = @{}

if ($Service -eq "all" -or $Service -eq "postgres") {
    $newCredentials.postgres = New-RandomPassword -Length 20
    Write-Host "🔐 Updating PostgreSQL credentials..." -ForegroundColor Blue
    kubectl patch secret postgres-secret -n $Environment -p='{"data":{"postgres-password":"'+ [Convert]::ToBase64String([Text.Encoding]::UTF8.GetBytes($newCredentials.postgres)) +'"}}'
}

if ($Service -eq "all" -or $Service -eq "minio") {
    $newCredentials.minio = New-RandomPassword -Length 20
    Write-Host "🔐 Updating MinIO credentials..." -ForegroundColor Blue
    kubectl patch secret minio-secret -n $Environment -p='{"data":{"secretkey":"'+ [Convert]::ToBase64String([Text.Encoding]::UTF8.GetBytes($newCredentials.minio)) +'"}}'
}

if ($Service -eq "all" -or $Service -eq "keycloak") {
    $newCredentials.keycloak = New-RandomPassword -Length 20
    Write-Host "🔐 Updating Keycloak credentials..." -ForegroundColor Blue
    kubectl patch secret keycloak-secret -n $Environment -p='{"data":{"admin-password":"'+ [Convert]::ToBase64String([Text.Encoding]::UTF8.GetBytes($newCredentials.keycloak)) +'"}}'
}

if ($Service -eq "all" -or $Service -eq "traefik") {
    $newCredentials.traefik = New-RandomPassword -Length 16
    Write-Host "🔐 Updating Traefik dashboard credentials..." -ForegroundColor Blue
    # Note: Traefik secret is in default namespace as it's shared
    $htpasswdEntry = "admin:`$2y`$10`$7EqJtq98hPqEX/.hN8I7..PaKLdPZ0C5P5nE5P5nE5P5nE5P5nE5P."
    kubectl patch secret traefik-dashboard-auth -n default -p='{"data":{"users":"'+ [Convert]::ToBase64String([Text.Encoding]::UTF8.GetBytes($htpasswdEntry)) +'"}}'
}

# Update credentials file
$credentialsFile = "$PSScriptRoot\..\credentials-$Environment.txt"
if (Test-Path $credentialsFile) {
    $content = Get-Content $credentialsFile -Raw
    
    if ($newCredentials.postgres) {
        $content = $content -replace "Password: .*", "Password: $($newCredentials.postgres)"
    }
    if ($newCredentials.minio) {
        $content = $content -replace "Secret Key: .*", "Secret Key: $($newCredentials.minio)"
    }
    if ($newCredentials.keycloak) {
        $content = $content -replace "Password: $($newCredentials.keycloak).*", "Password: $($newCredentials.keycloak)"
    }
    if ($newCredentials.traefik) {
        $content = $content -replace "Password: $($newCredentials.traefik).*", "Password: $($newCredentials.traefik)"
    }
    
    $content | Out-File -FilePath $credentialsFile -Encoding UTF8
}

Write-Host "`n📋 Updated credentials:" -ForegroundColor Yellow
$newCredentials.GetEnumerator() | ForEach-Object {
    Write-Host "$($_.Key): $($_.Value)" -ForegroundColor Cyan
}

Write-Host "`n⚠️  Note: You need to restart the affected pods for the new credentials to take effect:" -ForegroundColor Yellow
if ($newCredentials.postgres) { Write-Host "kubectl rollout restart statefulset/postgres -n default" -ForegroundColor Gray }
if ($newCredentials.minio) { Write-Host "kubectl rollout restart statefulset/minio -n default" -ForegroundColor Gray }
if ($newCredentials.keycloak) { Write-Host "kubectl rollout restart statefulset/keycloak -n default" -ForegroundColor Gray }
if ($newCredentials.traefik) { Write-Host "kubectl rollout restart deployment/traefik -n default" -ForegroundColor Gray }

Write-Host "`n✅ Credentials update completed!" -ForegroundColor Green
