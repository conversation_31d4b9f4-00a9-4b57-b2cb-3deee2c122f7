#!/usr/bin/env pwsh

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("dev", "staging", "prod")]
    [string]$Environment
)

Write-Host "🚀 Quick Deploy to $Environment environment..." -ForegroundColor Green

# Generate random passwords
function New-RandomPassword {
    param([int]$Length = 16)
    $chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    return -join ((1..$Length) | ForEach {$chars[(Get-Random -Maximum $chars.Length)]})
}

$postgresPassword = New-RandomPassword -Length 20
$minioSecretKey = New-RandomPassword -Length 20
$keycloakPassword = New-RandomPassword -Length 20
$traefikPassword = New-RandomPassword -Length 16

Write-Host "📦 Creating namespace: $Environment" -ForegroundColor Blue
kubectl create namespace $Environment --dry-run=client -o yaml | kubectl apply -f -

Write-Host "🔐 Creating secrets..." -ForegroundColor Blue

# Create PostgreSQL secret
kubectl create secret generic postgres-secret `
  --from-literal=postgres-user=admin `
  --from-literal=postgres-password=$postgresPassword `
  --namespace=$Environment `
  --dry-run=client -o yaml | kubectl apply -f -

# Create MinIO secret
kubectl create secret generic minio-secret `
  --from-literal=accesskey=admin `
  --from-literal=secretkey=$minioSecretKey `
  --namespace=$Environment `
  --dry-run=client -o yaml | kubectl apply -f -

# Create Keycloak secret
kubectl create secret generic keycloak-secret `
  --from-literal=admin-username=admin `
  --from-literal=admin-password=$keycloakPassword `
  --namespace=$Environment `
  --dry-run=client -o yaml | kubectl apply -f -

# Create Traefik dashboard auth secret (using htpasswd format)
$htpasswdEntry = "admin:`$2y`$10`$7EqJtq98hPqEX/.hN8I7..PaKLdPZ0C5P5nE5P5nE5P5nE5P5nE5P."
kubectl create secret generic traefik-dashboard-auth `
  --from-literal=users=$htpasswdEntry `
  --namespace=default `
  --dry-run=client -o yaml | kubectl apply -f -

Write-Host "🏗️ Deploying base services..." -ForegroundColor Blue
kubectl apply -f "$PSScriptRoot\..\base\" --namespace=default

Write-Host "🌐 Deploying environment ingress..." -ForegroundColor Blue
kubectl apply -f "$PSScriptRoot\..\environments\$Environment\" --namespace=$Environment

# Save credentials
$credentialsContent = @"
=== $Environment Environment Credentials ===

🌐 Service URLs:
- Traefik Dashboard: https://traefik-$Environment.yourdomain.com
- MinIO Console: https://minio-$Environment.yourdomain.com
- MinIO API: https://minio-api-$Environment.yourdomain.com
- Keycloak: https://keycloak-$Environment.yourdomain.com

🔑 Admin Credentials:
- PostgreSQL:
  Username: admin
  Password: $postgresPassword
  Database: $Environment

- MinIO:
  Access Key: admin
  Secret Key: $minioSecretKey
  Bucket: $Environment-bucket

- Keycloak:
  Username: admin
  Password: $keycloakPassword
  Realm: shared-realm

- Traefik Dashboard:
  Username: admin
  Password: $traefikPassword
"@

$credentialsFile = "$PSScriptRoot\..\credentials-$Environment.txt"
$credentialsContent | Out-File -FilePath $credentialsFile -Encoding UTF8

Write-Host "✅ Deployment completed!" -ForegroundColor Green
Write-Host "📋 Credentials saved to: $credentialsFile" -ForegroundColor Yellow
Write-Host ""
Write-Host "🔍 Check deployment status:" -ForegroundColor Blue
Write-Host "kubectl get all -n $Environment" -ForegroundColor Gray
Write-Host "kubectl get all -n default" -ForegroundColor Gray
