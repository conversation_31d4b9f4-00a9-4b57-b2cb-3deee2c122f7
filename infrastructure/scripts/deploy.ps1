#!/usr/bin/env pwsh

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("dev", "staging", "prod")]
    [string]$Environment,
    
    [Parameter(Mandatory=$false)]
    [string]$Namespace = $Environment,
    
    [Parameter(Mandatory=$false)]
    [switch]$DryRun
)

# Function to generate random password
function New-RandomPassword {
    param(
        [int]$Length = 16
    )
    $chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*"
    $password = ""
    for ($i = 0; $i -lt $Length; $i++) {
        $password += $chars[(Get-Random -Minimum 0 -Maximum $chars.Length)]
    }
    return $password
}

# Function to generate base64 encoded string
function ConvertTo-Base64 {
    param([string]$Text)
    $bytes = [System.Text.Encoding]::UTF8.GetBytes($Text)
    return [System.Convert]::ToBase64String($bytes)
}

# Function to create htpasswd entry
function New-HtpasswdEntry {
    param(
        [string]$Username,
        [string]$Password
    )
    # Using bcrypt for htpasswd (traefik compatible)
    $salt = "$2a$10$" + -join ((1..22) | ForEach {Get-Random -input ([char[]]"./ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789")})
    # For simplicity, we'll use a basic hash. In production, use proper bcrypt
    $hash = [System.Web.Security.Membership]::GeneratePassword(60, 0)
    return "${Username}:$hash"
}

Write-Host "🚀 Deploying to $Environment environment..." -ForegroundColor Green

# Generate passwords
$postgresPassword = New-RandomPassword -Length 20
$minioAccessKey = "admin"
$minioSecretKey = New-RandomPassword -Length 20
$keycloakPassword = New-RandomPassword -Length 20
$traefikDashboardPassword = New-RandomPassword -Length 16

# Generate client secrets for Keycloak
$clientSecrets = @{
    "cms-client" = New-RandomPassword -Length 32
    "api-client" = New-RandomPassword -Length 32
    "frontend-client" = New-RandomPassword -Length 32
}

Write-Host "📝 Generated credentials:" -ForegroundColor Yellow
Write-Host "PostgreSQL Admin Password: $postgresPassword" -ForegroundColor Cyan
Write-Host "MinIO Access Key: $minioAccessKey" -ForegroundColor Cyan
Write-Host "MinIO Secret Key: $minioSecretKey" -ForegroundColor Cyan
Write-Host "Keycloak Admin Password: $keycloakPassword" -ForegroundColor Cyan
Write-Host "Traefik Dashboard Password: $traefikDashboardPassword" -ForegroundColor Cyan
Write-Host "Client Secrets:" -ForegroundColor Cyan
$clientSecrets.GetEnumerator() | ForEach-Object { 
    Write-Host "  $($_.Key): $($_.Value)" -ForegroundColor Cyan 
}

# Create namespace
Write-Host "📦 Creating namespace: $Namespace" -ForegroundColor Blue
if (-not $DryRun) {
    kubectl create namespace $Namespace --dry-run=client -o yaml | kubectl apply -f -
}

# Create secrets
$secretsDir = "$PSScriptRoot\..\environments\$Environment\secrets"
New-Item -ItemType Directory -Force -Path $secretsDir | Out-Null

# PostgreSQL secret
$postgresSecret = @"
apiVersion: v1
kind: Secret
metadata:
  name: postgres-secret
  namespace: $Namespace
type: Opaque
data:
  postgres-password: $([Convert]::ToBase64String([Text.Encoding]::UTF8.GetBytes($postgresPassword)))
  postgres-user: $([Convert]::ToBase64String([Text.Encoding]::UTF8.GetBytes("admin")))
"@

# MinIO secret
$minioSecret = @"
apiVersion: v1
kind: Secret
metadata:
  name: minio-secret
  namespace: $Namespace
type: Opaque
data:
  accesskey: $([Convert]::ToBase64String([Text.Encoding]::UTF8.GetBytes($minioAccessKey)))
  secretkey: $([Convert]::ToBase64String([Text.Encoding]::UTF8.GetBytes($minioSecretKey)))
"@

# Keycloak secret
$keycloakSecret = @"
apiVersion: v1
kind: Secret
metadata:
  name: keycloak-secret
  namespace: $Namespace
type: Opaque
data:
  admin-password: $([Convert]::ToBase64String([Text.Encoding]::UTF8.GetBytes($keycloakPassword)))
  admin-username: $([Convert]::ToBase64String([Text.Encoding]::UTF8.GetBytes("admin")))
"@

# Traefik dashboard secret
$traefikSecret = @"
apiVersion: v1
kind: Secret
metadata:
  name: traefik-dashboard-auth
  namespace: $Namespace
type: Opaque
data:
  users: $([Convert]::ToBase64String([Text.Encoding]::UTF8.GetBytes("admin:`$apr1`$H6uskkkW`$IgXLP6ewTrSuBkTrqE8wj/")))
"@

# Save secrets to files
$postgresSecret | Out-File -FilePath "$secretsDir\postgres-secret.yaml" -Encoding UTF8
$minioSecret | Out-File -FilePath "$secretsDir\minio-secret.yaml" -Encoding UTF8
$keycloakSecret | Out-File -FilePath "$secretsDir\keycloak-secret.yaml" -Encoding UTF8
$traefikSecret | Out-File -FilePath "$secretsDir\traefik-secret.yaml" -Encoding UTF8

# Apply secrets
Write-Host "🔐 Applying secrets..." -ForegroundColor Blue
if (-not $DryRun) {
    kubectl apply -f "$secretsDir\"
}

# Apply base deployments
Write-Host "🏗️ Applying base deployments..." -ForegroundColor Blue
$baseDir = "$PSScriptRoot\..\base"
if (-not $DryRun) {
    kubectl apply -f "$baseDir\" -n $Namespace
}

# Apply environment-specific configurations
Write-Host "⚙️ Applying environment configurations..." -ForegroundColor Blue
$envDir = "$PSScriptRoot\..\environments\$Environment"
if (-not $DryRun) {
    kubectl apply -f "$envDir\" -n $Namespace
}

# Save credentials to file
$credentialsFile = "$PSScriptRoot\..\credentials-$Environment.txt"
$credentials = @"
=== $Environment Environment Credentials ===

🌐 Service URLs:
- Traefik Dashboard: https://traefik-$Environment.yourdomain.com
- PostgreSQL: postgres-service.$Namespace.svc.cluster.local:5432
- MinIO Console: https://minio-$Environment.yourdomain.com
- MinIO API: https://minio-api-$Environment.yourdomain.com
- Keycloak: https://keycloak-$Environment.yourdomain.com

🔑 Admin Credentials:
- PostgreSQL:
  Username: admin
  Password: $postgresPassword
  Database: $Environment

- MinIO:
  Access Key: $minioAccessKey
  Secret Key: $minioSecretKey
  Bucket: $Environment-bucket

- Keycloak:
  Username: admin
  Password: $keycloakPassword
  Realm: shared-realm

- Traefik Dashboard:
  Username: admin
  Password: $traefikDashboardPassword

🔧 Client Secrets (Keycloak):
"@

$clientSecrets.GetEnumerator() | ForEach-Object {
    $credentials += "`n- $($_.Key): $($_.Value)"
}

$credentials | Out-File -FilePath $credentialsFile -Encoding UTF8

Write-Host "✅ Deployment completed!" -ForegroundColor Green
Write-Host "📋 Credentials saved to: $credentialsFile" -ForegroundColor Yellow
Write-Host ""
Write-Host "🔍 Check deployment status:" -ForegroundColor Blue
Write-Host "kubectl get all -n $Namespace" -ForegroundColor Gray
Write-Host ""
Write-Host "📊 Monitor pods:" -ForegroundColor Blue
Write-Host "kubectl get pods -n $Namespace -w" -ForegroundColor Gray
